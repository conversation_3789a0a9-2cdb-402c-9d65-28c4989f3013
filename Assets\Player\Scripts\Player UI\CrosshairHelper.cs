using UnityEngine;
using System.Collections.Generic;

/// <summary>
/// Classe utilitária para facilitar a configuração de crosshairs
/// </summary>
public static class CrosshairHelper
{
    /// <summary>
    /// Cria um InteractHintData básico com texto
    /// </summary>
    public static InteractHintData CreateBasicHint(string text)
    {
        return new InteractHintData
        {
            InteractionText = text,
            InteractionSprite = null
        };
    }
    
    /// <summary>
    /// Cria um InteractHintData completo com texto e sprite
    /// </summary>
    public static InteractHintData CreateCompleteHint(string text, Sprite sprite)
    {
        return new InteractHintData
        {
            InteractionText = text,
            InteractionSprite = sprite
        };
    }
    
    /// <summary>
    /// Configura um InteractHint para um objeto interagível
    /// </summary>
    public static void SetupInteractHint(InteractableObject obj, string text, Sprite sprite = null, bool hasCondition = false)
    {
        if (obj.interactHint == null)
        {
            obj.interactHint = new InteractHint();
        }

        obj.interactHint.interactionHint = CreateCompleteHint(text, sprite);
        obj.interactHint.HasCondition = hasCondition;
    }

    /// <summary>
    /// Creates an animated InteractHintData with typewriter effect
    /// </summary>
    public static InteractHintData CreateAnimatedHint(string text, float typewriterSpeed = 0.05f, Color? textColor = null)
    {
        return new InteractHintData
        {
            InteractionText = text,
            UseAnimatedText = true,
            UseTypewriter = true,
            DelayPerCharacter = typewriterSpeed,
            TextColor = textColor ?? Color.white,
            FontSize = 18f
        };
    }

    /// <summary>
    /// Creates an animated InteractHintData with custom text effects
    /// </summary>
    public static InteractHintData CreateAnimatedHintWithEffects(string text, List<TextEffectBase> effects,
        float typewriterSpeed = 0.05f, Color? textColor = null)
    {
        return new InteractHintData
        {
            InteractionText = text,
            UseAnimatedText = true,
            UseTypewriter = true,
            DelayPerCharacter = typewriterSpeed,
            TextColor = textColor ?? Color.white,
            FontSize = 18f,
            TextEffects = effects ?? new List<TextEffectBase>()
        };
    }

    /// <summary>
    /// Creates an animated InteractHintData using a pre-configured VisualTextDocument
    /// </summary>
    public static InteractHintData CreateAnimatedHintWithDocument(VisualTextDocument document, Sprite sprite = null)
    {
        return new InteractHintData
        {
            InteractionText = "", // Not used when document is provided
            InteractionSprite = sprite,
            UseAnimatedText = true,
            AnimatedTextDocument = document
        };
    }

    /// <summary>
    /// Configura um InteractHint para um objeto interagível com texto animado
    /// </summary>
    public static void SetupAnimatedInteractHint(InteractableObject obj, string text,
        float typewriterSpeed = 0.05f, Color? textColor = null, Sprite sprite = null, bool hasCondition = false)
    {
        if (obj.interactHint == null)
        {
            obj.interactHint = new InteractHint();
        }

        obj.interactHint.interactionHint = CreateAnimatedHint(text, typewriterSpeed, textColor);
        obj.interactHint.interactionHint.InteractionSprite = sprite;
        obj.interactHint.HasCondition = hasCondition;
    }

    /// <summary>
    /// Configura um InteractHint para um objeto interagível com efeitos de texto customizados
    /// </summary>
    public static void SetupAnimatedInteractHintWithEffects(InteractableObject obj, string text,
        List<TextEffectBase> effects, float typewriterSpeed = 0.05f, Color? textColor = null,
        Sprite sprite = null, bool hasCondition = false)
    {
        if (obj.interactHint == null)
        {
            obj.interactHint = new InteractHint();
        }

        obj.interactHint.interactionHint = CreateAnimatedHintWithEffects(text, effects, typewriterSpeed, textColor);
        obj.interactHint.interactionHint.InteractionSprite = sprite;
        obj.interactHint.HasCondition = hasCondition;
    }
}