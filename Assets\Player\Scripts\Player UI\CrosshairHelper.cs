using UnityEngine;

/// <summary>
/// Classe utilitária para facilitar a configuração de crosshairs
/// </summary>
public static class CrosshairHelper
{
    /// <summary>
    /// Cria um InteractHintData básico com texto
    /// </summary>
    public static InteractHintData CreateBasicHint(string text)
    {
        return new InteractHintData
        {
            InteractionText = text,
            InteractionSprite = null
        };
    }
    
    /// <summary>
    /// Cria um InteractHintData completo com texto e sprite
    /// </summary>
    public static InteractHintData CreateCompleteHint(string text, Sprite sprite)
    {
        return new InteractHintData
        {
            InteractionText = text,
            InteractionSprite = sprite
        };
    }
    
    /// <summary>
    /// Configura um InteractHint para um objeto interagível
    /// </summary>
    public static void SetupInteractHint(InteractableObject obj, string text, Sprite sprite = null, bool hasCondition = false)
    {
        if (obj.interactHint == null)
        {
            obj.interactHint = new InteractHint();
        }
        
        obj.interactHint.interactionHint = CreateCompleteHint(text, sprite);
        obj.interactHint.HasCondition = hasCondition;
    }
}