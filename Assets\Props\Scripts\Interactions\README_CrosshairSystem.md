# Sistema de Crosshair para Objetos Interagíveis

Este sistema permite que o crosshair mude automaticamente quando o player olha para objetos interagíveis.

## Como Configurar

### 1. Configuração do CrosshairManager

No Inspector do `CrosshairManager`:
- **CrosshairImage**: Referência para o componente Image do crosshair na UI
- **DefaultSprite**: Sprite padrão do crosshair
- **InteractableSprite**: Sprite que aparece quando olhando para objetos interagíveis
- **CrosshairText**: Referência para o componente TextMeshProUGUI que mostra o texto de interação

### 2. Configuração do FPSController

Certifique-se de que o `FPSController` tem a referência para o `CrosshairManager` no campo `crosshairManager`.

### 3. Configurando Objetos Interagíveis

#### Opção A: Usando CrosshairInteractionHelper (Recomendado)

1. Adicione o componente `CrosshairInteractionHelper` ao seu objeto interagível
2. Configure as opções no Inspector:
   - **Custom Crosshair Sprite**: Sprite personalizado (opcional)
   - **Interaction Text**: Texto a ser exibido ("Pressione E para interagir")
   - **Has Conditions**: Se deve verificar condições antes de mostrar
   - **Conditions**: Lista de condições (se aplicável)

#### Opção B: Configuração Manual

No seu script que herda de `InteractableObject`:

```csharp
void Start()
{
    // Configura InteractHint
    interactHint = new InteractHint
    {
        InteractionText = "Pressione E para interagir",
        InteractionSprite = meuSpritePersonalizado // opcional
    };
    
    // Configura HighlightSettings
    interactHint = new InteractHint
    {
        crosshairTextOverride = "Pressione E para interagir",
        crosshairIconOverride = meuSpritePersonalizado, // opcional
        HasCondition = false
    };
}
```

## Como Funciona

1. O `FPSController` detecta quando o player olha para um objeto interagível
2. Quando o objeto está dentro da distância de interação, o sistema:
   - Chama `UpdateCrosshairForInteractable()` 
   - Verifica se há configurações de highlight personalizadas
   - Atualiza o crosshair com o sprite e texto apropriados
3. Quando o player para de olhar ou sai da distância, o crosshair volta ao padrão

## Prioridade das Configurações

1. **InteractHint.crosshairIconOverride** (maior prioridade)
2. **InteractHint.InteractionSprite**
3. **CrosshairManager.InteractableSprite** (sprite padrão para interagíveis)

## Exemplo de Uso

```csharp
public class MinhaPorta : InteractableObject
{
    void Start()
    {
        // Configuração simples usando o helper
        var helper = gameObject.AddComponent<CrosshairInteractionHelper>();
        helper.crosshairSettings.interactionText = "Abrir Porta";
        helper.crosshairSettings.customCrosshairSprite = spritePorta;
    }
    
    public override void OnStartInteract(IGameEntity sender)
    {
        // Lógica para abrir a porta
        Debug.Log("Abrindo porta...");
    }
}
```

## Dicas

- Use sprites pequenos e claros para o crosshair (32x32 ou 64x64 pixels)
- Mantenha o texto de interação curto e claro
- Teste a visibilidade do crosshair em diferentes backgrounds
- Use condições para mostrar diferentes textos baseados no estado do jogo