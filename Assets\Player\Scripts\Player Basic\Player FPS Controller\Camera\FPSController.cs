using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.InputSystem;
using UnityEngine.UI;
using Unity.Cinemachine;
using PlayerCore;

public class FPSController : MonoBehaviour
{
    public Transform playerBody;
    public float verticalLookLimit = 90f;
    public float sensitivity = 2f;

    public float lookSmoothTime = 0.1f; // Ajuste para mais ou menos suavização
    private Quaternion playerTargetRot;
    private Quaternion cameraTargetRot;
    public PlayerInputHandler MyInput;

    public float raycastDistance = 10f;
    public LayerMask raycastLayerMask;
    public float fieldOfViewAngle = 45f;
    public float fieldOfViewDistance = 10f;

    public GameObject detectedObject;
    public float DistanciaInteração = 5.0f;
    public IFocusDetection currentFocus;
    public Transform InteractionPointTransform;
    public List<IFieldOfViewDetection> ObjectsInView = new List<IFieldOfViewDetection>();

    public Camera playerCamera;
    public Transform cameraHolder;
    public CinemachineBrain cinemachineBrain;
    public CinemachineCamera thirdPersonCamera;

    public HeadBobManager headBobManager;

    public CrosshairManager crosshairManager;
    public PlayerMonoBehaviour PlayerMono;

    public delegate void ViewEventHandler(object reciever);

    private IInteractable _currentlyTrackedInteractableForDistance = null;
    private bool _isInteractableInDistance = false;

    void Start() // Awake é chamado antes de Start. MyInput deve estar configurado por PlayerMonoBehaviour.Initialize
    {
        Cursor.lockState = CursorLockMode.Locked;
        playerTargetRot = playerBody.localRotation;
        cameraTargetRot = cameraHolder.localRotation;

        MyInput.RegisterCallback(this.HandleLookInput, "Look");
    }

    private void HandleLookInput(InputAction.CallbackContext context)
    {
        Vector2 lookInput = context.ReadValue<Vector2>();
        float finalMouseX = lookInput.x * sensitivity;
        float finalMouseY = lookInput.y * sensitivity;

        // Calcula as rotações alvo
        playerTargetRot *= Quaternion.Euler(0f, finalMouseX, 0f);
        cameraTargetRot *= Quaternion.Euler(-finalMouseY, 0f, 0f);

        // Limita a rotação vertical (pitch)
        cameraTargetRot = ClampRotationAroundXAxis(cameraTargetRot, -verticalLookLimit, verticalLookLimit);

        // Aplica a suavização usando Slerp
        playerBody.localRotation = Quaternion.Slerp(playerBody.localRotation, playerTargetRot, lookSmoothTime); // Usamos Time.deltaTime implícito no Slerp
        cameraHolder.localRotation = Quaternion.Slerp(cameraHolder.localRotation, cameraTargetRot, lookSmoothTime);
    }

    void Update()
    {
        PerformRaycastFromCameraCenter();
        DetectObjectsInFieldOfView();
    }

    private void PerformRaycastFromCameraCenter()
    {
        Ray ray = new Ray(cameraHolder.transform.position, cameraHolder.transform.forward);
        RaycastHit hit;
        bool didHit = Physics.Raycast(ray, out hit, raycastDistance, raycastLayerMask);

        IFocusDetection newViewTarget = null;
        MonoBehaviour newViewTargetMonoBehaviour = null;

        if (didHit)
        {
            detectedObject = hit.collider.gameObject;
            newViewTarget = hit.collider.GetComponent<IFocusDetection>();
            newViewTargetMonoBehaviour = hit.collider.GetComponent<MonoBehaviour>();
        }
        else
        {
            detectedObject = null;
        }

        // Handle IFocusDetection (Focus)
        if (newViewTarget != currentFocus)
        {
            if (currentFocus != null)
            {
                currentFocus.EndView(PlayerMono.GetUser());

                // If the object losing focus was an interactable and was in range, call OnInteractDistanceExit
                IInteractable oldInteractableFocus = currentFocus as IInteractable;
                if (oldInteractableFocus != null && oldInteractableFocus == _currentlyTrackedInteractableForDistance && _isInteractableInDistance)
                {
                    oldInteractableFocus.OnInteractDistanceExit(PlayerMono);
                    // _isInteractableInDistance will be reset below or re-evaluated for the new target
                }
            }

            currentFocus = newViewTarget;
            _currentlyTrackedInteractableForDistance = currentFocus as IInteractable; // Track the new focus for distance checks
            _isInteractableInDistance = false; // Reset distance state for the new/potentially null interactable

            if (currentFocus != null)
            {
                currentFocus.StartView(PlayerMono.GetUser());
            }
        }
        else if (currentFocus != null) // newViewTarget == currentFocus, and it's not null
        {
            currentFocus.ContinueView(PlayerMono.GetUser());
        }

        // Handle IInteractable Distance for the *current focused object*
        // This part runs regardless of focus change, to handle moving in/out of range of a continuously focused object.
        IInteractable currentFocusedInteractable = currentFocus as IInteractable;
        MonoBehaviour currentFocusedMonoBehaviour = currentFocus as MonoBehaviour;

        if (currentFocusedInteractable != null && currentFocusedMonoBehaviour != null)
        {
            // Ensure we are tracking this interactable if it's the current focus
            _currentlyTrackedInteractableForDistance = currentFocusedInteractable;

            float dist = Vector3.Distance(cameraHolder.transform.position, currentFocusedMonoBehaviour.transform.position);
            bool nowInRange = dist < DistanciaInteração;

            if (nowInRange && !_isInteractableInDistance)
            {
                Debug.Log("OnInteractDistanceEnter 4 " + currentFocusedMonoBehaviour.name);
                if(InteractionPointTransform != null) InteractionPointTransform.position = hit.point;
                // MyInput implements IPlayer which extends IGameEntity
                _currentlyTrackedInteractableForDistance.OnInteractDistanceEnter(PlayerMono);
                _isInteractableInDistance = true;
                
                // Atualiza crosshair para mostrar que é interagível
                UpdateCrosshairForInteractable(currentFocusedInteractable);
            }
            else if (!nowInRange && _isInteractableInDistance)
            {
                // MyInput implements IPlayer which extends IGameEntity
                _currentlyTrackedInteractableForDistance.OnInteractDistanceExit(PlayerMono);
                _isInteractableInDistance = false;
                
                // Volta crosshair ao padrão
                if (crosshairManager != null)
                {
                    crosshairManager.SetDefaultCrosshair();
                }
            }
        }
        else if (_currentlyTrackedInteractableForDistance != null && _isInteractableInDistance)
        {
            // Current focus is null or not interactable, but we were tracking an interactable that was in range.
            // This means we looked away from an in-range interactable.
            _currentlyTrackedInteractableForDistance.OnInteractDistanceExit(PlayerMono);
            _currentlyTrackedInteractableForDistance = null;
            _isInteractableInDistance = false;
            
            // Volta crosshair ao padrão
            if (crosshairManager != null)
            {
                crosshairManager.SetDefaultCrosshair();
            }
        }
        
        // Garante que o crosshair volte ao padrão quando não há objeto interagível
        if (currentFocusedInteractable == null && crosshairManager != null && crosshairManager.IsShowingInteractable())
        {
            crosshairManager.SetDefaultCrosshair();
        }
    }

    void OnDisable() // Importante desregistrar os callbacks para evitar erros
    {
        if (MyInput != null) {
            MyInput.UnregisterCallback(this.HandleLookInput, "Look"); // Adicionado para consistência com Start
        }
    }

    private void DetectObjectsInFieldOfView()
    {
        Collider[] colliders = Physics.OverlapSphere(cameraHolder.transform.position, fieldOfViewDistance, raycastLayerMask);

        // Lista temporária para armazenar os InteractableObjects atualmente visíveis.
        List<IFieldOfViewDetection> currentVisibleInteractables = new List<IFieldOfViewDetection>();
        List<IFieldOfViewDetection> toRemove = new List<IFieldOfViewDetection>();

        foreach (Collider collider in colliders)
        {
            Vector3 directionToObject = (collider.transform.position - cameraHolder.transform.position).normalized;
            float angleToObject = Vector3.Angle(cameraHolder.transform.forward, directionToObject);

            if (angleToObject <= fieldOfViewAngle)
            {
                IFieldOfViewDetection interactable = collider.GetComponent<IFieldOfViewDetection>();

                if(interactable != null)
                {
                    // Adiciona à lista temporária
                    currentVisibleInteractables.Add(interactable);

                    if(!ObjectsInView.Contains(interactable) && FPSUtils.IsOnPlayerView(cameraHolder, collider.transform)){
                        interactable.EnterFieldOfView(PlayerMono.GetUser());
                        ObjectsInView.Add(interactable);
                    }
                    else if(ObjectsInView.Contains(interactable) && FPSUtils.IsOnPlayerView(cameraHolder, collider.transform)){
                        interactable.StayInFieldOfView(PlayerMono.GetUser());
                    }
                    else if(ObjectsInView.Contains(interactable) && !FPSUtils.IsOnPlayerView(cameraHolder, collider.transform)){
                        interactable.ExitFieldOfView(PlayerMono.GetUser());
                        toRemove.Add(interactable);
                    }
                }
            }
        }

        foreach (var interactable in toRemove)
        {
            ObjectsInView.Remove(interactable);
        }
    }

    // Função auxiliar para limitar a rotação vertical
    private Quaternion ClampRotationAroundXAxis(Quaternion q, float minAngle, float maxAngle)
    {
        q.x /= q.w;
        q.y /= q.w;
        q.z /= q.w;
        q.w = 1.0f;

        float angleX = 2.0f * Mathf.Rad2Deg * Mathf.Atan(q.x);
        angleX = Mathf.Clamp(angleX, minAngle, maxAngle);
        q.x = Mathf.Tan(0.5f * Mathf.Deg2Rad * angleX);

        return q;
    }

    private void UpdateCrosshairForInteractable(IInteractable interactable)
    {
        if (crosshairManager == null) return;
        
        // Obtém as configurações de hint do objeto
        InteractHintData hint = interactable.GetInteractHint();
        crosshairManager.SetInteractableCrosshair(hint);
    }

    private void OnDrawGizmos()
    {
        if (cameraHolder == null) return;
    
        // Desenha o raio que representa a linha de visão central (PerformRaycastFromCameraCenter)
        Gizmos.color = Color.red;
        Gizmos.DrawLine(cameraHolder.transform.position, cameraHolder.transform.position + cameraHolder.transform.forward * raycastDistance);

        // Se houver um objeto detectado (detectado com o raycast), desenha uma esfera para marcá-lo
        if (detectedObject != null)
        {
            Gizmos.color = Color.magenta;
            Gizmos.DrawSphere(detectedObject.transform.position, 0.2f);
        }

        // Desenha a esfera que delimita a distância de alcance do Field Of View
        Gizmos.color = Color.yellow;
        Gizmos.DrawWireSphere(cameraHolder.transform.position, fieldOfViewDistance);

        // Calcula o campo de visão real da câmera
        float fov = playerCamera.fieldOfView;  // Campo de visão da câmera
        float aspect = playerCamera.aspect;  // Proporção da tela (largura/altura)
        float halfFovHorizontal = Mathf.Atan(Mathf.Tan(fov * Mathf.Deg2Rad / 2) * aspect) * Mathf.Rad2Deg;

        // Desenha as linhas laterais do campo de visão
        Vector3 forward = cameraHolder.transform.forward;
        Quaternion leftRotation = Quaternion.Euler(0, -halfFovHorizontal, 0);
        Quaternion rightRotation = Quaternion.Euler(0, halfFovHorizontal, 0);
        Vector3 leftBoundary = leftRotation * forward;
        Vector3 rightBoundary = rightRotation * forward;

        Gizmos.color = Color.green;
        Gizmos.DrawRay(cameraHolder.transform.position, leftBoundary * fieldOfViewDistance);
        Gizmos.DrawRay(cameraHolder.transform.position, rightBoundary * fieldOfViewDistance);
    }
}