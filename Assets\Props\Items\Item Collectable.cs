using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;
using UnityEngine.InputSystem;
using UnityEngine.InputSystem.Interactions;
using UnityEditor;

public class ItemCollectable : InteractableObject
{
    public InputAction action;
    public ItemSO item; // O item associado a este objeto no mundo
    public int amount = 4; // amount do item a ser pega
    public bool collectOnlyOneStackAtATime = true; // Novo: Se true, coleta apenas o suficiente para um stack por interação.
    public bool canCollect = true;

    public GameObject examinePanelPrefab; // Arraste seu prefab do Painel de Exame aqui

    public InputCallConfig InteractBinding;
    public InputCallConfig ExamineBinding;

    public void Awake()
    {
        InteractBinding = new InputCallConfig();
        ExamineBinding = new InputCallConfig();       

        // Certifique-se de que o GameManager foi inicializado
        if (GameManager.Instance != null && GameManager.Instance.inputActions != null)
        {
            // Preencher InteractBinding.ActionReference
            InputAction interactAction = GameManager.Instance.inputActions.FindAction("Interact");
            if (interactAction != null){
                ////InteractBinding.AddStep(interactAction);
            }
            else
            {
                Debug.LogWarning($"Ação '{"Interact"}' não encontrada no InputActionAsset do GameManager. Verifique o nome.");
            }

            // Preencher ExamineBinding.ActionReference
            InputAction examineAction = GameManager.Instance.inputActions.FindAction("Drop");
            if (examineAction != null){
                ////ExamineBinding.AddStep(examineAction);
            }
            else{
                Debug.LogWarning($"Ação '{"Drop"}' não encontrada no InputActionAsset do GameManager. Verifique o nome.");
            }
        }
    }

    public virtual void StartInteract(object sender) { }
    public virtual void Interact(object sender){}
    public virtual void EndInteract(object sender){}

    public override void OnInteractDistanceEnter(IGameEntity entity)
    {
        Debug.Log("Interact Distance enter 1");
        if(entity is IPlayer player)
        {
            PlayerMonoBehaviour playerMonoBehaviour = player.GetPlayerComponent();
            PlayerInputHandler inputHandler = playerMonoBehaviour.GetInputHandler();
            Debug.Log("Interact Distance enter 2");
            if (inputHandler == null) return;
            Debug.Log("Interact Distance enter 3");

            // Registrar callback lambda usando a nova API que armazena a referência no PlayerInputHandler
            inputHandler.RegisterLambdaCallback(this, "Interact", (System.Func<InputAction.CallbackContext, IEnumerator>)(ctx => TryCollect(entity, ctx)));
            inputHandler.RegisterCallback(ExamineItem, "Examine");
        }
    }

    public override void OnInteractDistanceExit(IGameEntity entity)
    {
        if(entity is IPlayer player)
        {
            PlayerMonoBehaviour playerMonoBehaviour = player.GetPlayerComponent();
            PlayerInputHandler inputHandler = playerMonoBehaviour.GetInputHandler();
            if(inputHandler == null) return;

            // Desregistrar o callback lambda usando a nova API (versão simples)
            inputHandler.UnregisterLambdaCallback(this, "Interact");
            inputHandler.UnregisterCallback(ExamineItem, "Examine");
        }
    }
    
    public IEnumerator TryCollect(IGameEntity entity, InputAction.CallbackContext context)
    {
        if (!context.started) yield break;

        Debug.Log(" Try collect 1");
        if (item == null || entity == null)
        {
            Debug.Log(" Try collect 2");
            Debug.LogWarning("ItemCollectable: Nenhum ItemSO atribuído para coleta.", this);
            yield break;
        }
        Debug.Log(" Try collect 3");
        Debug.Log($"Tentando coletar {amount} de {item.itemName} para o inventário", this);
        if (entity is IPlayer player)
        {
            Debug.Log(" Try collect 4");
            float timeToCollect = 3f;
            float timer = 0;

            while (context.action.IsPressed() && timer < timeToCollect)
            {
                Debug.Log(" Try collect 5");
                timer += Time.deltaTime;
                yield return null;
            }

            if(timer >= timeToCollect)
            {
                Debug.Log(" Try collect 8");
                CollectItem(entity, context);
            }
        }
        yield break;
    }

    public void CollectItem(IGameEntity entity, InputAction.CallbackContext context)
    {
        int amountAdded = 0;

        if (entity is IPlayer player)
        {
            Debug.Log(" coletando intem");
            IInventory inventory = player.Inventory();

            if(inventory == null) Debug.Log("Inventory is null");

            amountAdded = inventory.AddItem(item, amount, collectOnlyOneStackAtATime);
        }

        amount -= amountAdded;
        if (amount <= 0)
        {
            Destroy(gameObject);
        }
    }

    private void AddToJournal(ActionActivationContext context)
    {
        // Substitua pela lógica de adicionar o item ao sistema de diário/notas.
        PlayerMonoBehaviour player = context.User.PlayerCharacterComponent;

    }

    private void AddKeyItem(ActionActivationContext context)
    {
        // Substitua pela lógica de adicionar o item ao sistema de chaves.
        PlayerMonoBehaviour player = context.User.PlayerCharacterComponent;
        
    }


    public void ExamineItem(ActionActivationContext context)
    {
        if (item == null) {
            Debug.LogWarning("ItemCollectable: Nenhum ItemSO atribuído para examinar.", this);
            return;
        }

        Debug.Log($"ItemCollectable: Solicitando exame para o item '{item.itemName}' pelo usuário {context.User.UserID}", this);
        // Chamar o UserUIManager para mostrar o painel de exame
        GameObject examinePanel = context.User.UIManager.InstantiateUIPanel(examinePanelPrefab, true);
        ExaminePanelUI examinePanelUI = examinePanel.GetComponent<ExaminePanelUI>();
    }

    public void ClearReference()
    {

    }

    public override void OnDestroy()
    {
        // Limpa todos os callbacks lambda registrados por este objeto quando for destruído
        // Isso garante que não haja vazamentos de memória ou callbacks órfãos
        if (PlayersManager.Instance != null && PlayersManager.Instance.ActiveUsers != null)
        {
            foreach (var user in PlayersManager.Instance.ActiveUsers)
            {
                if (user != null && user.PlayerCharacterComponent != null)
                {
                    PlayerInputHandler inputHandler = user.GetInputHandler();
                    if (inputHandler != null)
                    {
                        inputHandler.UnregisterAllLambdaCallbacks(this);
                    }
                }
            }
        }

        // Chama o OnDestroy da classe base
        base.OnDestroy();
    }
}