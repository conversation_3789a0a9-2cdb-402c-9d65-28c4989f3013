%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1001 &3261574835583481969
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: -8679921383154817045, guid: 616a2e9681c8d2640aac5f922a071e14, type: 3}
      propertyPath: m_LocalPosition.x
      value: 5.44
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 616a2e9681c8d2640aac5f922a071e14, type: 3}
      propertyPath: m_LocalPosition.y
      value: 1.9
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 616a2e9681c8d2640aac5f922a071e14, type: 3}
      propertyPath: m_LocalPosition.z
      value: 3.0997515
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 616a2e9681c8d2640aac5f922a071e14, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 616a2e9681c8d2640aac5f922a071e14, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 616a2e9681c8d2640aac5f922a071e14, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 616a2e9681c8d2640aac5f922a071e14, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 616a2e9681c8d2640aac5f922a071e14, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 616a2e9681c8d2640aac5f922a071e14, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 616a2e9681c8d2640aac5f922a071e14, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 919132149155446097, guid: 616a2e9681c8d2640aac5f922a071e14, type: 3}
      propertyPath: m_Name
      value: Garbage Bag
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents:
    - targetCorrespondingSourceObject: {fileID: 919132149155446097, guid: 616a2e9681c8d2640aac5f922a071e14, type: 3}
      insertIndex: -1
      addedObject: {fileID: 3803160396399534280}
    - targetCorrespondingSourceObject: {fileID: 919132149155446097, guid: 616a2e9681c8d2640aac5f922a071e14, type: 3}
      insertIndex: -1
      addedObject: {fileID: 6874531462823371628}
    - targetCorrespondingSourceObject: {fileID: 919132149155446097, guid: 616a2e9681c8d2640aac5f922a071e14, type: 3}
      insertIndex: -1
      addedObject: {fileID: -5040095055698575723}
  m_SourcePrefab: {fileID: 100100000, guid: 616a2e9681c8d2640aac5f922a071e14, type: 3}
--- !u!1 &2414519673451398432 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 919132149155446097, guid: 616a2e9681c8d2640aac5f922a071e14, type: 3}
  m_PrefabInstance: {fileID: 3261574835583481969}
  m_PrefabAsset: {fileID: 0}
--- !u!65 &3803160396399534280
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2414519673451398432}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 7.944824, y: 3.656679, z: 3.930334}
  m_Center: {x: -1.819562, y: 0.6638795, z: -0.17004013}
--- !u!114 &6874531462823371628
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2414519673451398432}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 26a036f416bbc294cb5fbe8f71f53b0f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  interactHint:
    crosshairIconOverride: {fileID: 0}
    crosshairTextOverride: 
    HasCondition: 0
    conditionSO: []
  action:
    m_Name: Action
    m_Type: 0
    m_ExpectedControlType: 
    m_Id: ec8ef7dc-ab0a-42ec-ac62-e9f5386ab591
    m_Processors: 
    m_Interactions: 
    m_SingletonActionBindings: []
    m_Flags: 0
  item: {fileID: 11400000, guid: e4bf2232d6f13ef4ab8e48438a8b1985, type: 2}
  amount: 2
  collectOnlyOneStackAtATime: 1
  canCollect: 1
  examinePanelPrefab: {fileID: 0}
  InteractBinding: {fileID: 0}
  ExamineBinding: {fileID: 0}
--- !u!54 &-5040095055698575723
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2414519673451398432}
  serializedVersion: 4
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
