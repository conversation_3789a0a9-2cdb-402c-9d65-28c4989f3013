using UnityEngine;
using UnityEditor;

#if UNITY_EDITOR
[CustomEditor(typeof(RectMask3DController))]
public class RectMask3DControllerEditor : Editor
{
    private SerializedProperty maskingRectProp;
    private SerializedProperty maskingMaterialProp;
    private SerializedProperty targetRendererProp;
    private SerializedProperty applyToAllChildRenderersProp;
    private SerializedProperty fadeDistanceProp;

    void OnEnable()
    {
        maskingRectProp = serializedObject.FindProperty("maskingRect");
        maskingMaterialProp = serializedObject.FindProperty("_maskedMaterial");
        targetRendererProp = serializedObject.FindProperty("targetRenderer");
        applyToAllChildRenderersProp = serializedObject.FindProperty("applyToAllChildRenderers");
        fadeDistanceProp = serializedObject.FindProperty("fadeDistance");
    }

    public override void OnInspectorGUI()
    {
        serializedObject.Update();
        
        RectMask3DController controller = (RectMask3DController)target;

        EditorGUILayout.LabelField("3D Rect Mask Controller", EditorStyles.boldLabel);
        EditorGUILayout.Space();

        // Masking Rect
        EditorGUILayout.PropertyField(maskingRectProp);
        EditorGUILayout.PropertyField(maskingMaterialProp);

        EditorGUILayout.Space();
        
        // Mode Selection
        EditorGUILayout.LabelField("Renderer Target Mode", EditorStyles.boldLabel);
        EditorGUILayout.PropertyField(applyToAllChildRenderersProp, new GUIContent("Apply to All Child Renderers", 
            "When enabled, automatically applies masking to all Renderer components found in this GameObject and its children."));

        if (!applyToAllChildRenderersProp.boolValue)
        {
            EditorGUILayout.PropertyField(targetRendererProp, new GUIContent("Target Renderer", 
                "Specific Renderer component to apply masking to."));
        }

        EditorGUILayout.Space();
        
        // Fade Distance
        EditorGUILayout.PropertyField(fadeDistanceProp);
        
        EditorGUILayout.Space();
        
        // Info Section
        EditorGUILayout.LabelField("Renderer Information", EditorStyles.boldLabel);
        
        if (Application.isPlaying || controller != null)
        {
            // Get current renderer count
            Renderer[] allRenderers = controller.GetComponentsInChildren<Renderer>();
            
            if (applyToAllChildRenderersProp.boolValue)
            {
                EditorGUILayout.LabelField($"Found Renderers: {allRenderers.Length}");
                
                if (allRenderers.Length > 0)
                {
                    EditorGUI.indentLevel++;
                    foreach (var renderer in allRenderers)
                    {
                        if (renderer != null)
                        {
                            EditorGUILayout.LabelField($"• {renderer.name} ({renderer.GetType().Name})");
                        }
                    }
                    EditorGUI.indentLevel--;
                }
            }
            else
            {
                if (targetRendererProp.objectReferenceValue != null)
                {
                    EditorGUILayout.LabelField("Target: " + targetRendererProp.objectReferenceValue.name);
                }
                else
                {
                    EditorGUILayout.HelpBox("No target renderer specified.", MessageType.Warning);
                }
            }
        }
        
        EditorGUILayout.Space();
        
        // Action Buttons
        EditorGUILayout.LabelField("Actions", EditorStyles.boldLabel);
        
        if (GUILayout.Button("Auto-detect All Renderers"))
        {
            controller.AutoDetectRenderers();
            EditorUtility.SetDirty(controller);
        }
        
        if (GUILayout.Button("Refresh Renderer List"))
        {
            // Force update by toggling the boolean
            bool current = applyToAllChildRenderersProp.boolValue;
            applyToAllChildRenderersProp.boolValue = !current;
            serializedObject.ApplyModifiedProperties();
            applyToAllChildRenderersProp.boolValue = current;
            serializedObject.ApplyModifiedProperties();
        }

        // Validation
        if (maskingRectProp.objectReferenceValue == null)
        {
            EditorGUILayout.HelpBox("Masking Rect is required for the mask to function.", MessageType.Error);
        }

        if (!applyToAllChildRenderersProp.boolValue && targetRendererProp.objectReferenceValue == null)
        {
            EditorGUILayout.HelpBox("Either enable 'Apply to All Child Renderers' or specify a Target Renderer.", MessageType.Warning);
        }

        serializedObject.ApplyModifiedProperties();
    }
}
#endif
