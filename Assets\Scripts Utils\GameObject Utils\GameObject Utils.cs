using UnityEngine;
using System.Collections;
using System.Collections.Generic;

public static class GameObjectUtils
{
    public static Object InstantiateInUI(Object original)
    {
        Object newObject = Object.Instantiate(original);
        if(newObject is GameObject gameObject) gameObject.AddComponent<MeshRectAdapter>();
        return newObject;
    }
    public static Object InstantiateInUI(Object original, Transform parent)
    {
        Object newObject = Object.Instantiate(original, parent);
        if(newObject is GameObject gameObject) gameObject.AddComponent<MeshRectAdapter>();
        return newObject;
    }
    public static Object InstantiateInUI(Object original, Transform parent, bool instantiateInWorldSpace)
    {
        Object newObject = Object.Instantiate(original, parent, instantiateInWorldSpace);
        if(newObject is GameObject gameObject) gameObject.AddComponent<MeshRectAdapter>();
        return newObject;
    }
    public static Object InstantiateInUI(Object original, Vector3 position, Quaternion rotation)
    {
        Object newObject = Object.Instantiate(original, position, rotation);
        if(newObject is GameObject gameObject) gameObject.AddComponent<MeshRectAdapter>();
        return newObject;
    }
    public static Object InstantiateInUI(Object original, Vector3 position, Quaternion rotation, Transform parent)
    {
        Object newObject = Object.Instantiate(original, position, rotation, parent);
        if(newObject is GameObject gameObject) gameObject.AddComponent<MeshRectAdapter>();
        return newObject;
    }

    /// <summary>
    /// Calcula o tamanho e o centro de um BoxCollider para que ele se ajuste
    /// a todos os renderers (malhas) de um GameObject e seus filhos.
    /// </summary>
    /// <param name="go">O GameObject para o qual o BoxCollider será ajustado.</param>
    public static void CalculateColliderSize(GameObject go)
    {
        // Obtém todos os renderers (componentes de malha) no GameObject e seus filhos.
        Renderer[] renderers = go.GetComponentsInChildren<Renderer>();

        // Se não houver renderers, não há malha para calcular o collider.
        if (renderers.Length == 0)
        {
            Debug.LogWarning("CalculateColliderSize: Nenhum renderer encontrado no GameObject " + go.name + " ou seus filhos. Nenhum BoxCollider será ajustado.");
            return;
        }

        // Inicializa os limites combinados com os limites do primeiro renderer.
        Bounds combinedBounds = renderers[0].bounds;

        // Percorre os renderers restantes e encapsula seus limites nos limites combinados.
        for (int i = 1; i < renderers.Length; i++)
        {
            combinedBounds.Encapsulate(renderers[i].bounds);
        }

        // Obtém o BoxCollider do GameObject. Se não existir, adiciona um.
        BoxCollider collider = go.GetComponent<BoxCollider>();
        if (collider == null)
        {
            collider = go.AddComponent<BoxCollider>();
        }

        // Define o centro e o tamanho do BoxCollider com base nos limites combinados.
        // É importante converter o centro dos limites globais para o espaço local do GameObject.
        collider.center = go.transform.InverseTransformPoint(combinedBounds.center);
        collider.size = combinedBounds.size;

        Debug.Log("BoxCollider ajustado para o GameObject: " + go.name +
                  " | Centro: " + collider.center +
                  " | Tamanho: " + collider.size);
    }

    /// <summary>
    /// Calculates the pivot position for a model such that its "true" center aligns with a desired world position.
    /// The "true" center is determined by the model's pivot plus its centerOffset,
    /// considering its defaultRotation.
    /// </summary>
    /// <param name="modelInfo">The ModelInfo object containing prefab, defaultRotation, and centerOffset.</param>
    /// <param name="desiredTrueCenterPosition">The world position where the model's true center should be.</param>
    /// <returns>The calculated world position for the model's pivot (transform.position).</returns>
    public static Vector3 ModelCenterOffset(GameObject modelInstance, ModelInfo modelInfo, Vector3 position)
    {
        if (modelInfo == null)
        {
            Debug.LogError("ModelCenterOffset: ModelInfo cannot be null. Returning the desired position as a fallback, assuming no offset.");
            return position; // Return the target position itself, implying no adjustment
        }

        // 1. Calculate the world-space offset from the model's pivot to its mesh center,
        // considering the model's current rotation and scale (lossyScale accounts for parent transforms).
        Vector3 meshCenterOffsetFromPivot = modelInstance.transform.rotation * Vector3.Scale(modelInfo.centerOffset, modelInstance.transform.lossyScale);

        // 2. Determine the new position for the model's transform.
        // newModelPosition = targetWorldPositionForMeshCenter - offsetOfMeshCenterFromPivot
        Vector3 newModelPosition = position - meshCenterOffsetFromPivot;

        return newModelPosition;
    }

    /// <summary>
    /// Calculates the combined AABB (Axis-Aligned Bounding Box) of all enabled renderers 
    /// within the GameObject hierarchy, as if the root GameObject (modelInstance) 
    /// were at identity rotation and unit (1,1,1) scale.
    /// This provides the "intrinsic" size of the model.
    /// Note: This method temporarily modifies the modelInstance's transform (rotation and scale).
    /// Ensure this is acceptable in your context (e.g., not called during physics updates or
    /// when other scripts are sensitive to immediate transform changes).
    /// </summary>
    /// <param name="modelInstance">The root GameObject of the model.</param>
    /// <returns>A Bounds object centered at Vector3.zero, with its size representing the intrinsic dimensions of the model.</returns>
    public static Bounds GetIntrinsicModelBounds(GameObject modelInstance)
    {
        if (modelInstance == null)
        {
            return new Bounds(Vector3.zero, Vector3.zero);
        }

        Vector3 originalPosition = modelInstance.transform.position;
        Quaternion originalRotation = modelInstance.transform.rotation;
        Vector3 originalScale = modelInstance.transform.localScale;

        modelInstance.transform.rotation = Quaternion.identity;
        modelInstance.transform.localScale = Vector3.one;

        Bounds combinedWorldBounds = new Bounds();
        bool firstBoundInitialized = false;
        Renderer[] renderers = modelInstance.GetComponentsInChildren<Renderer>();

        if (renderers.Length == 0)
        {
            modelInstance.transform.position = originalPosition;
            modelInstance.transform.rotation = originalRotation;
            modelInstance.transform.localScale = originalScale;
            return new Bounds(Vector3.zero, Vector3.zero);
        }

        foreach (Renderer renderer in renderers)
        {
            if (!renderer.enabled) continue;
            if (!firstBoundInitialized)
            {
                combinedWorldBounds = renderer.bounds;
                firstBoundInitialized = true;
            }
            else
            {
                combinedWorldBounds.Encapsulate(renderer.bounds);
            }
        }

        modelInstance.transform.position = originalPosition;
        modelInstance.transform.rotation = originalRotation;
        modelInstance.transform.localScale = originalScale;

        if (!firstBoundInitialized) return new Bounds(Vector3.zero, Vector3.zero);

        return new Bounds(Vector3.zero, combinedWorldBounds.size);
    }

    /// <summary>
    /// Posiciona um GameObject para que seja totalmente enquadrado por uma câmera.
    /// </summary>
    /// <param name="gameObject">O GameObject a ser enquadrado.</param>
    /// <param name="camera">A câmera que enquadrará o objeto.</param>
    /// <param name="threshold">Uma margem percentual para adicionar ao enquadramento (ex: 0.1 para 10% de margem).</param>
    /// <returns>A posição ideal do GameObject para ser enquadrado pela câmera.</returns>
    public static Vector3 FitObjectToCamera(Camera camera, GameObject gameObject, float threshold = 0.1f) { return FitObjectToCamera(gameObject, camera, threshold); }
    public static Vector3 FitObjectToCamera(GameObject gameObject, Camera camera, float threshold = 0.1f)
    {
        // Obtém os limites do objeto
        Bounds bounds = GetObjectBounds(gameObject);
        
        // Calcula o tamanho do objeto com a margem adicional
        float objectSize = bounds.size.magnitude * (1 + threshold);
        
        // Calcula o campo de visão vertical em radianos
        float fovRadians = camera.fieldOfView * Mathf.Deg2Rad;
        
        // Calcula a distância necessária para enquadrar o objeto
        float distance = objectSize / (2 * Mathf.Tan(fovRadians / 2));
        
        // Calcula a posição ideal do objeto (em vez da câmera)
        Vector3 objectPosition = camera.transform.position + camera.transform.forward * distance;
        
        return objectPosition;
    }

    /// <summary>
    /// Obtém os limites de um objeto incluindo todos os seus renderers filhos.
    /// </summary>
    /// <param name="gameObject">O GameObject a ter seus limites calculados.</param>
    /// <returns>Os limites (Bounds) do objeto.</returns>
    private static Bounds GetObjectBounds(GameObject gameObject)
    {
        Renderer[] renderers = gameObject.GetComponentsInChildren<Renderer>();
        
        if (renderers.Length == 0)
            return new Bounds(gameObject.transform.position, Vector3.one);
        
        Bounds bounds = renderers[0].bounds;
        
        for (int i = 1; i < renderers.Length; i++)
        {
            bounds.Encapsulate(renderers[i].bounds);
        }
        
        return bounds;
    }

}
