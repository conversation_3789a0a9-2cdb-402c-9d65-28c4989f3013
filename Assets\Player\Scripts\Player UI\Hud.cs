using UnityEngine;
using UnityEngine.UI;
using TMPro;
public class PlayerHUD : MonoBehaviour
{
    public CrosshairManager crossHairManager;
    public TextMeshProUGUI health;
    public TextMeshProUGUI stamina;
    public TextMeshProUGUI heartRate;
    public TextMeshProUGUI sanity;
    public TextMeshProUGUI progress;

    public PlayerStatus playerStatus;

    public void Start()
    {
        playerStatus.OnHealthChanged += UpdateText;
        playerStatus.OnStaminaChanged += UpdateText;
        playerStatus.OnHeartRateChanged += UpdateText;
        playerStatus.OnSanityChanged += UpdateText;
    }

    public void UpdateText()
    {
        
    }

    public void UpdateProgress(PlayerInputHandler playerInputHandler, float value)
    {
        progress.text = "Progress: " + value;
    }
}
