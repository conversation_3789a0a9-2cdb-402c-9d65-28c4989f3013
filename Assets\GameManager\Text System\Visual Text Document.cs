using UnityEngine;
using UnityEditor;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Text;
using System.Text.RegularExpressions;
using TMPro;

/// <summary>
/// Representa um documento de texto visual, contendo múltiplas sentenças
/// com suas respectivas configurações de exibição.
/// Aplica configurações padrão a sentenças que não são definidas por chaves {}.
/// </summary>

[CreateAssetMenu(fileName = "New Visual Text Document", menuName = "Game Manager/Text System/Visual Text Document")]
public class VisualTextDocument : InstantiableSO
{
    [TextAreaAttribute]
    public string fullText;

    [Tooltip("Lista de frases processadas, cada uma contendo suas próprias sentenças.")]
    public List<PhraseData> Phrases = new List<PhraseData>();

    [Header("Configurações Padrão para Texto Normal (Fora das Chaves)")]
    [Tooltip("Cor padrão para texto normal.")]
    public Color DefaultTextColor = Color.black; // Alterado para preto para diferenciar do padrão de SentenceDisplayData

    [Tooltip("Fonte padrão para texto normal. Se nulo, usará a fonte do componente TextMeshPro.")]
    public TMP_FontAsset DefaultFontAsset = null;

    [Tooltip("Tamanho da fonte padrão para texto normal.")]
    public float DefaultFontSize = 30; // Alterado para diferenciar

    [Tooltip("Se o texto normal padrão deve ser negrito.")]
    public bool DefaultIsBold = false;

    [Tooltip("Se o texto normal padrão deve ser itálico.")]
    public bool DefaultIsItalic = false;

    [Tooltip("Alinhamento padrão para texto normal.")]
    public TextAlignmentOptions DefaultAlignment = TextAlignmentOptions.Left;

    [Tooltip("Atraso padrão após sentenças de texto normal.")]
    public float DefaultDelayAfterSentence = 0.5f;

    [Tooltip("Atraso padrão após frases (parágrafos) de texto normal.")]
    public float DefaultDelayAfterPhrase = 1.0f; // Um pouco maior para ser notável entre frases

    [Header("Configurações Padrão de Máquina de Escrever")]
    [Tooltip("Se o efeito de máquina de escrever deve ser usado por padrão.")]
    public bool DefaultUseTypewriter = true;
    [Tooltip("Atraso padrão por caractere para o efeito de máquina de escrever.")]
    public float DefaultDelayPerCharacter = 0.05f;
    [Tooltip("Se o efeito deve pausar em pontuações por padrão.")]
    public bool DefaultPauseOnPunctuation = true;
    [Tooltip("Atraso padrão em pontuações.")]
    public float DefaultPunctuationDelay = 0.2f;

    [Tooltip("Som de digitação padrão para o efeito de máquina de escrever.")]
    public AudioEffect DefaultTypewriterSound;

    /// <summary>
    /// Processa a string bruta, divide em sentenças e aplica configurações.
    /// Usa comparação inteligente para preservar configurações de sentenças que não mudaram.
    /// </summary>
    public void ParseAndConfigureText(string rawText)
    {
        if (string.IsNullOrEmpty(rawText))
        {
            Phrases.Clear();
            return;
        }

        // Se o texto não mudou, não faz nada
        if (rawText == fullText && Phrases.Count > 0)
        {
            return;
        }

        // Usa o sistema inteligente de comparação
        ParseAndConfigureTextIntelligent(rawText);
    }

    /// <summary>
    /// Sistema inteligente de parse que preserva configurações de sentenças inalteradas.
    /// </summary>
    private void ParseAndConfigureTextIntelligent(string rawText)
    {
        try
        {
            // Salva as configurações atuais antes de processar
            var previousPhrases = new List<PhraseData>(Phrases.Count);
            for (int i = 0; i < Phrases.Count; i++)
            {
                previousPhrases.Add(DeepCopyPhrase(Phrases[i]));
            }

            // Processa o novo texto temporariamente
            var newPhrases = ParseTextToNewPhrases(rawText);

            // Compara e mescla as configurações
            MergePhrasesIntelligently(previousPhrases, newPhrases);

            // Atualiza o fullText
            fullText = rawText;
        }
        catch (RegexMatchTimeoutException ex)
        {
            Debug.LogError($"Regex timeout during text parsing: {ex.Message}");
        }
        catch (Exception ex)
        {
            Debug.LogError($"Error during text parsing: {ex.Message}");
        }
    }

    /// <summary>
    /// Processa o texto em novas frases sem afetar as atuais.
    /// </summary>
    private List<PhraseData> ParseTextToNewPhrases(string rawText)
    {
        var newPhrases = new List<PhraseData>();

        string[] phraseBlocks = rawText.Split(new[] { "\n\n", "\r\n\r\n" }, StringSplitOptions.RemoveEmptyEntries);

        foreach (string phraseBlock in phraseBlocks)
        {
            var currentPhrase = new PhraseData();
            currentPhrase.OriginalText = phraseBlock; // Salva o texto bruto
            string processedPhraseBlock = phraseBlock;

            // Verifica se a frase tem um delay customizado com uma tag customizada <delay_phrase value="X" />
            var delayPhraseRegex = RegexCache.GetRegex(@"<delay_phrase value=[""'](.*?)[""']\s*/?>", RegexOptions.IgnoreCase);
            if (delayPhraseRegex == null) continue;
            Match delayPhraseMatch = delayPhraseRegex.Match(processedPhraseBlock);
            if (delayPhraseMatch.Success && float.TryParse(delayPhraseMatch.Groups[1].Value, System.Globalization.NumberStyles.Any, System.Globalization.CultureInfo.InvariantCulture, out float delayPhraseValue))
            {
                currentPhrase.DelayAfterPhrase = delayPhraseValue;
                processedPhraseBlock = processedPhraseBlock.Replace(delayPhraseMatch.Value, "").Trim();
            }
            else currentPhrase.DelayAfterPhrase = DefaultDelayAfterPhrase; // Aplica o delay padrão se a tag não for encontrada

            // 2. Divide cada bloco de frase em sentenças usando uma regex mais poderosa
            // Esta regex divide o texto por {blocos}, <clear/>, <delete/>, mantendo-os como delimitadores.
            var splitRegex = RegexCache.GetRegex(@"({.*?}|<clear\s*/>|<delete\s*/>)");
            if (splitRegex == null) continue;
            string[] parts = splitRegex.Split(processedPhraseBlock);

            foreach (string part in parts)
            {
                if (string.IsNullOrEmpty(part.Trim())) continue;

                SentenceDisplayData sentenceData;

                if (part.StartsWith("{") && part.EndsWith("}"))
                {
                    // É um bloco de configuração
                    sentenceData = new SentenceDisplayData(part.Substring(1, part.Length - 2), true);
                    // O processamento de tags internas (mat, font, etc.) acontece abaixo
                }
                else
                {
                    // É texto normal
                    sentenceData = new SentenceDisplayData(part, false);
                }

                if (!sentenceData.IsFromBraces) 
                {
                    // Aplica as configurações padrão do VisualTextDocument
                    sentenceData.TextColor = DefaultTextColor;
                    sentenceData.FontAsset = DefaultFontAsset;
                    sentenceData.FontSize = DefaultFontSize;
                    sentenceData.IsBold = DefaultIsBold;
                    sentenceData.IsItalic = DefaultIsItalic;
                    sentenceData.Alignment = DefaultAlignment;
                    sentenceData.DelayAfterSentence = DefaultDelayAfterSentence;
                    sentenceData.UseTypewriter = DefaultUseTypewriter;
                    sentenceData.DelayPerCharacter = DefaultDelayPerCharacter;
                    sentenceData.PauseOnPunctuation = DefaultPauseOnPunctuation;
                    sentenceData.PunctuationDelay = DefaultPunctuationDelay;
                    sentenceData.TypewriterSound = DefaultTypewriterSound;
                }
                else if (sentenceData.IsFromBraces)
                {
                    // Para texto de dentro das chaves {}, processamos as tags customizadas.

                    // Parse para <pause value="1.5">
                    var pauseRegex = RegexCache.GetRegex(@"<pause value=[""'](.*?)[""']\s*/?>", RegexOptions.IgnoreCase);
                    Match pauseMatch = pauseRegex?.Match(sentenceData.Text) ?? Match.Empty;
                    if (pauseMatch.Success && float.TryParse(pauseMatch.Groups[1].Value, System.Globalization.NumberStyles.Any, System.Globalization.CultureInfo.InvariantCulture, out float pauseValue))
                    {
                        sentenceData.DelayAfterSentence = pauseValue;
                        sentenceData.Text = sentenceData.Text.Replace(pauseMatch.Value, "").Trim();
                    }

                    // Parse para <mat name="NomeDoMaterial">
                    var matRegex = RegexCache.GetRegex(@"<mat name=[""'](.*?)[""']\s*/?>", RegexOptions.IgnoreCase);
                    Match matMatch = matRegex?.Match(sentenceData.Text) ?? Match.Empty;
                    if (matMatch.Success)
                    {
                        string matName = matMatch.Groups[1].Value;
#if UNITY_EDITOR
                        string[] guids = AssetDatabase.FindAssets($"t:Material {matName}");
                        if (guids.Length > 0) {
                            string path = AssetDatabase.GUIDToAssetPath(guids[0]);
                            sentenceData.TextMaterial = AssetDatabase.LoadAssetAtPath<Material>(path);
                        }
#endif
                        sentenceData.Text = sentenceData.Text.Replace(matMatch.Value, "").Trim();
                    }

                    // Parse para <font name="NomeDaFonte">
                    var fontRegex = RegexCache.GetRegex(@"<font name=[""'](.*?)[""']\s*/?>", RegexOptions.IgnoreCase);
                    Match fontMatch = fontRegex?.Match(sentenceData.Text) ?? Match.Empty;
                    if (fontMatch.Success)
                    {
                        string fontName = fontMatch.Groups[1].Value;
#if UNITY_EDITOR
                        string[] guids = AssetDatabase.FindAssets($"t:TMP_FontAsset {fontName}");
                        if (guids.Length > 0) {
                            string path = AssetDatabase.GUIDToAssetPath(guids[0]);
                            sentenceData.FontAsset = AssetDatabase.LoadAssetAtPath<TMP_FontAsset>(path);
                        } else {
                            Debug.LogWarning($"VisualTextDocument: Não foi possível encontrar a fonte TMP_FontAsset com o nome '{fontName}'.");
                        }
#endif
                        sentenceData.Text = sentenceData.Text.Replace(fontMatch.Value, "").Trim();
                    }

                    // Parse para <typewriter use="false" delay="0.02" p_delay="0.1" p_pause="false" />
                    var typewriterRegex = RegexCache.GetRegex(@"<typewriter\s*(.*?)\s*/?>", RegexOptions.IgnoreCase);
                    Match typewriterMatch = typewriterRegex?.Match(sentenceData.Text) ?? Match.Empty;
                    if (typewriterMatch.Success)
                    {
                        string attributes = typewriterMatch.Groups[1].Value;

                        // Parse 'use'
                        var useRegex = RegexCache.GetRegex(@"use=[""'](true|false)[""']", RegexOptions.IgnoreCase);
                        Match useMatch = useRegex?.Match(attributes) ?? Match.Empty;
                        if (useMatch.Success) sentenceData.UseTypewriter = bool.Parse(useMatch.Groups[1].Value);

                        // Parse 'delay'
                        var delayRegex = RegexCache.GetRegex(@"delay=[""'](.*?)[""']", RegexOptions.IgnoreCase);
                        Match delayMatch = delayRegex?.Match(attributes) ?? Match.Empty;
                        if (delayMatch.Success && float.TryParse(delayMatch.Groups[1].Value, System.Globalization.NumberStyles.Any, System.Globalization.CultureInfo.InvariantCulture, out float delayValue))
                        {
                            sentenceData.DelayPerCharacter = delayValue;
                        }

                        // Parse 'p_delay' (punctuation delay)
                        var pDelayRegex = RegexCache.GetRegex(@"p_delay=[""'](.*?)[""']", RegexOptions.IgnoreCase);
                        Match pDelayMatch = pDelayRegex?.Match(attributes) ?? Match.Empty;
                        if (pDelayMatch.Success && float.TryParse(pDelayMatch.Groups[1].Value, System.Globalization.NumberStyles.Any, System.Globalization.CultureInfo.InvariantCulture, out float pDelayValue))
                        {
                            sentenceData.PunctuationDelay = pDelayValue;
                        }

                        // Parse 'p_pause' (pause on punctuation)
                        var pPauseRegex = RegexCache.GetRegex(@"p_pause=[""'](true|false)[""']", RegexOptions.IgnoreCase);
                        Match pPauseMatch = pPauseRegex?.Match(attributes) ?? Match.Empty;
                        if (pPauseMatch.Success) sentenceData.PauseOnPunctuation = bool.Parse(pPauseMatch.Groups[1].Value);

                            // Remove a tag do texto final
                            sentenceData.Text = sentenceData.Text.Replace(typewriterMatch.Value, "").Trim();
                        }

                        // Parse para <sound name="NomeDoAudioEffect">
                        var soundRegex = RegexCache.GetRegex(@"<sound name=[""'](.*?)[""']\s*/?>", RegexOptions.IgnoreCase);
                        Match soundMatch = soundRegex?.Match(sentenceData.Text) ?? Match.Empty;
                        if (soundMatch.Success)
                        {
                            string soundName = soundMatch.Groups[1].Value;
    #if UNITY_EDITOR
                            string[] guids = AssetDatabase.FindAssets($"t:AudioEffect {soundName}");
                            if (guids.Length > 0) {
                                string path = AssetDatabase.GUIDToAssetPath(guids[0]);
                                sentenceData.TypewriterSound = AssetDatabase.LoadAssetAtPath<AudioEffect>(path);
                            } else {
                                Debug.LogWarning($"VisualTextDocument: Não foi possível encontrar o AudioEffect com o nome '{soundName}'.");
                            }
    #endif
                            sentenceData.Text = sentenceData.Text.Replace(soundMatch.Value, "").Trim();
                        }
                }
                currentPhrase.Sentences.Add(sentenceData);
            }

            // Concatena o texto de todas as sentenças para formar o FullPhraseText
            var phraseTextBuilder = new StringBuilder(currentPhrase.Sentences.Count * 50); // Pre-allocate
            for (int i = 0; i < currentPhrase.Sentences.Count; i++)
            {
                phraseTextBuilder.Append(currentPhrase.Sentences[i].Text);
                if (i < currentPhrase.Sentences.Count - 1)
                    phraseTextBuilder.Append(" ");
            }
            currentPhrase.FullPhraseText = phraseTextBuilder.ToString();
            newPhrases.Add(currentPhrase);
        }

        return newPhrases;
    }

    /// <summary>
    /// Compara e mescla as frases antigas com as novas, preservando configurações de sentenças inalteradas.
    /// </summary>
    private void MergePhrasesIntelligently(List<PhraseData> previousPhrases, List<PhraseData> newPhrases)
    {
        Phrases.Clear();

        // Create a dictionary for faster lookup
        var previousPhrasesDict = new Dictionary<string, PhraseData>(previousPhrases.Count);
        for (int i = 0; i < previousPhrases.Count; i++)
        {
            var phrase = previousPhrases[i];
            if (!string.IsNullOrEmpty(phrase.OriginalText))
                previousPhrasesDict[phrase.OriginalText] = phrase;
        }

        for (int i = 0; i < newPhrases.Count; i++)
        {
            var newPhrase = newPhrases[i];
            previousPhrasesDict.TryGetValue(newPhrase.OriginalText, out PhraseData matchingPreviousPhrase);

            if (matchingPreviousPhrase != null)
            {
                // Frase existe, mescla as sentenças inteligentemente
                MergeSentencesIntelligently(matchingPreviousPhrase, newPhrase);

                // Preserva configurações de nível de frase que não são do parse
                newPhrase.DelayAfterPhrase = matchingPreviousPhrase.DelayAfterPhrase;
                newPhrase.RequireSkipToAdvance = matchingPreviousPhrase.RequireSkipToAdvance;
            }

            Phrases.Add(newPhrase);
        }
    }

    /// <summary>
    /// Mescla sentenças de uma frase, preservando configurações de sentenças que não mudaram.
    /// </summary>
    private void MergeSentencesIntelligently(PhraseData previousPhrase, PhraseData newPhrase)
    {
        // Create dictionary for faster sentence lookup
        var previousSentencesDict = new Dictionary<string, SentenceDisplayData>(previousPhrase.Sentences.Count);
        for (int j = 0; j < previousPhrase.Sentences.Count; j++)
        {
            var sentence = previousPhrase.Sentences[j];
            string key = $"{sentence.Text}_{sentence.IsFromBraces}_{sentence.Action}";
            if (!previousSentencesDict.ContainsKey(key))
                previousSentencesDict[key] = sentence;
        }

        for (int i = 0; i < newPhrase.Sentences.Count; i++)
        {
            var newSentence = newPhrase.Sentences[i];
            string key = $"{newSentence.Text}_{newSentence.IsFromBraces}_{newSentence.Action}";
            previousSentencesDict.TryGetValue(key, out SentenceDisplayData matchingPreviousSentence);

            if (matchingPreviousSentence != null)
            {
                // Sentença existe e não mudou, preserva todas as configurações customizadas
                CopyCustomSentenceSettings(matchingPreviousSentence, newSentence);
            }
        }
    }

    /// <summary>
    /// Verifica se duas sentenças são equivalentes (mesmo texto e tipo).
    /// </summary>
    private bool SentencesAreEquivalent(SentenceDisplayData sentence1, SentenceDisplayData sentence2)
    {
        return sentence1.Text == sentence2.Text &&
               sentence1.IsFromBraces == sentence2.IsFromBraces &&
               sentence1.Action == sentence2.Action;
    }

    /// <summary>
    /// Copia configurações customizadas de uma sentença para outra, preservando personalizações.
    /// </summary>
    private void CopyCustomSentenceSettings(SentenceDisplayData source, SentenceDisplayData target)
    {
        // Preserva ID se foi customizado
        if (!string.IsNullOrEmpty(source.SentenceID))
            target.SentenceID = source.SentenceID;

        // Preserva configurações visuais customizadas
        if (!source.FollowBaseColor)
            target.TextColor = source.TextColor;

        if (source.FontAsset != null)
            target.FontAsset = source.FontAsset;

        if (!source.FollowBaseFontSize)
            target.FontSize = source.FontSize;

        target.IsBold = source.IsBold;
        target.IsItalic = source.IsItalic;
        target.Alignment = source.Alignment;
        target.FollowBaseColor = source.FollowBaseColor;
        target.FollowBaseFontSize = source.FollowBaseFontSize;

        // Preserva configurações de typewriter
        target.UseTypewriter = source.UseTypewriter;
        target.DelayPerCharacter = source.DelayPerCharacter;
        target.PauseOnPunctuation = source.PauseOnPunctuation;
        target.PunctuationDelay = source.PunctuationDelay;
        target.TypewriterSound = source.TypewriterSound;
        target.DelayAfterSentence = source.DelayAfterSentence;

        // Preserva efeitos e materiais
        if (source.TextEffects != null && source.TextEffects.Count > 0)
            target.TextEffects = new List<TextEffectBase>(source.TextEffects);

        if (source.DeleteEffects != null && source.DeleteEffects.Count > 0)
            target.DeleteEffects = new List<TextEffectBase>(source.DeleteEffects);

        if (source.TextMaterial != null)
            target.TextMaterial = source.TextMaterial;

        if (source.ImageSprite != null)
            target.ImageSprite = source.ImageSprite;

        // Preserva eventos
        target.OnSentenceStart = source.OnSentenceStart;
        target.OnSentenceEnd = source.OnSentenceEnd;

        // Preserva configurações de controle
        target.RequireSkipToAdvance = source.RequireSkipToAdvance;

        // Preserva configurações de ação
        target.Action = source.Action;
        target.TargetSentenceID = source.TargetSentenceID;
        target.DeleteSpeed = source.DeleteSpeed;
        target.DeleteSound = source.DeleteSound;

        // Preserva sistema de replace
        if (source.ReplacementOptions != null && source.ReplacementOptions.Count > 0)
            target.ReplacementOptions = new List<SentenceDisplayData>(source.ReplacementOptions);
        target.CurrentReplacementIndex = source.CurrentReplacementIndex;
        target.LoopReplacements = source.LoopReplacements;
        target.ReplaceDelay = source.ReplaceDelay;
    }

    /// <summary>
    /// Cria uma cópia profunda de uma PhraseData.
    /// </summary>
    private PhraseData DeepCopyPhrase(PhraseData original)
    {
        var copy = new PhraseData
        {
            OriginalText = original.OriginalText,
            FullPhraseText = original.FullPhraseText,
            DelayAfterPhrase = original.DelayAfterPhrase,
            RequireSkipToAdvance = original.RequireSkipToAdvance,
            Sentences = new List<SentenceDisplayData>()
        };

        foreach (var sentence in original.Sentences)
        {
            copy.Sentences.Add(DeepCopySentence(sentence));
        }

        return copy;
    }

    /// <summary>
    /// Cria uma cópia profunda de uma SentenceDisplayData.
    /// </summary>
    private SentenceDisplayData DeepCopySentence(SentenceDisplayData original)
    {
        var copy = new SentenceDisplayData
        {
            Action = original.Action,
            SentenceID = original.SentenceID,
            Text = original.Text,
            IsFromBraces = original.IsFromBraces,
            TextColor = original.TextColor,
            FollowBaseColor = original.FollowBaseColor,
            FontSize = original.FontSize,
            FollowBaseFontSize = original.FollowBaseFontSize,
            IsBold = original.IsBold,
            IsItalic = original.IsItalic,
            Alignment = original.Alignment,
            FontAsset = original.FontAsset,
            UseTypewriter = original.UseTypewriter,
            DelayPerCharacter = original.DelayPerCharacter,
            PauseOnPunctuation = original.PauseOnPunctuation,
            PunctuationDelay = original.PunctuationDelay,
            TypewriterSound = original.TypewriterSound,
            DelayAfterSentence = original.DelayAfterSentence,
            ImageSprite = original.ImageSprite,
            TextMaterial = original.TextMaterial,
            RequireSkipToAdvance = original.RequireSkipToAdvance,
            OnSentenceStart = original.OnSentenceStart,
            OnSentenceEnd = original.OnSentenceEnd,
            TargetSentenceID = original.TargetSentenceID,
            DeleteSpeed = original.DeleteSpeed,
            DeleteSound = original.DeleteSound,
            CurrentReplacementIndex = original.CurrentReplacementIndex,
            LoopReplacements = original.LoopReplacements,
            ReplaceDelay = original.ReplaceDelay
        };

        // Copia listas de efeitos
        if (original.TextEffects != null)
        {
            copy.TextEffects = new List<TextEffectBase>(original.TextEffects);
        }

        if (original.DeleteEffects != null)
        {
            copy.DeleteEffects = new List<TextEffectBase>(original.DeleteEffects);
        }

        // Copia lista de replacement options
        if (original.ReplacementOptions != null)
        {
            copy.ReplacementOptions = new List<SentenceDisplayData>(original.ReplacementOptions);
        }

        return copy;
    }

    /// <summary>
    /// Processa o texto de uma única PhraseData, atualizando sua lista de sentenças.
    /// </summary>
    /// <param name="phraseToParse">A instância de PhraseData a ser reprocessada.</param>
    public void ParseAndConfigureSinglePhrase(PhraseData phraseToParse)
    {
        // AVISO: Esta função precisaria da mesma refatoração de parse acima para funcionar com as novas tags.
        if (phraseToParse == null || string.IsNullOrEmpty(phraseToParse.OriginalText))
        {
            return;
        }

        // Limpa as sentenças antigas da frase
        phraseToParse.Sentences.Clear();

        string processedPhraseBlock = phraseToParse.OriginalText;

        string delayPhrasePattern = @"<delay_phrase value=[""'](.*?)[""']\s*/?>";
        Match delayPhraseMatch = Regex.Match(processedPhraseBlock, delayPhrasePattern, RegexOptions.IgnoreCase);
        if (delayPhraseMatch.Success && float.TryParse(delayPhraseMatch.Groups[1].Value, System.Globalization.NumberStyles.Any, System.Globalization.CultureInfo.InvariantCulture, out float delayPhraseValue))
        {
            phraseToParse.DelayAfterPhrase = delayPhraseValue;
            processedPhraseBlock = processedPhraseBlock.Replace(delayPhraseMatch.Value, "").Trim();
        }
        else
        {
            phraseToParse.DelayAfterPhrase = DefaultDelayAfterPhrase;
        }

        // Re-divide em sentenças
        List<SentenceDisplayData> parsedSentences = processedPhraseBlock.SplitIntoConfigurableSentences();

        foreach (var sentenceData in parsedSentences)
        {
            if (!sentenceData.IsFromBraces)
            {
                // Aplica as configurações padrão do VisualTextDocument
                sentenceData.TextColor = DefaultTextColor;
                sentenceData.FontAsset = DefaultFontAsset;
                sentenceData.FontSize = DefaultFontSize;
                sentenceData.IsBold = DefaultIsBold;
                sentenceData.IsItalic = DefaultIsItalic;
                sentenceData.Alignment = DefaultAlignment;
                sentenceData.DelayAfterSentence = DefaultDelayAfterSentence;
                sentenceData.UseTypewriter = DefaultUseTypewriter;
                sentenceData.DelayPerCharacter = DefaultDelayPerCharacter;
                sentenceData.PauseOnPunctuation = DefaultPauseOnPunctuation;
                sentenceData.PunctuationDelay = DefaultPunctuationDelay;
                sentenceData.TypewriterSound = DefaultTypewriterSound;
            }
            else
            {
                // Re-processa as tags de nível de sentença
                // (Este código é duplicado do ParseAndConfigureText principal para consistência)
                string speedPattern = @"<speed value=[""'](.*?)[""']\s*/?>";
                Match speedMatch = Regex.Match(sentenceData.Text, speedPattern, RegexOptions.IgnoreCase);

                string pausePattern = @"<pause value=[""'](.*?)[""']\s*/?>";
                Match pauseMatch = Regex.Match(sentenceData.Text, pausePattern, RegexOptions.IgnoreCase);
                if (pauseMatch.Success && float.TryParse(pauseMatch.Groups[1].Value, System.Globalization.NumberStyles.Any, System.Globalization.CultureInfo.InvariantCulture, out float pauseValue)) { sentenceData.DelayAfterSentence = pauseValue; sentenceData.Text = sentenceData.Text.Replace(pauseMatch.Value, "").Trim(); }

                string matPattern = @"<mat name=[""'](.*?)[""']\s*/?>";
                Match matMatch = Regex.Match(sentenceData.Text, matPattern, RegexOptions.IgnoreCase);
                if (matMatch.Success) 
                { 
                    string matName = matMatch.Groups[1].Value; 
                    #if UNITY_EDITOR 
                    string[] guids = AssetDatabase.FindAssets($"t:Material {matName}"); 
                    if (guids.Length > 0) 
                    { 
                        string path = AssetDatabase.GUIDToAssetPath(guids[0]); 
                        sentenceData.TextMaterial = AssetDatabase.LoadAssetAtPath<Material>(path); 
                    } 
                    #endif 
                    sentenceData.Text = sentenceData.Text.Replace(matMatch.Value, "").Trim(); 
                }

                string fontPattern = @"<font name=[""'](.*?)[""']\s*/?>";
                Match fontMatch = Regex.Match(sentenceData.Text, fontPattern, RegexOptions.IgnoreCase);
                if (fontMatch.Success) 
                { 
                    string fontName = fontMatch.Groups[1].Value; 
                    #if UNITY_EDITOR 
                    string[] guids = AssetDatabase.FindAssets($"t:TMP_FontAsset {fontName}"); 
                    if (guids.Length > 0) 
                    { 
                        string path = AssetDatabase.GUIDToAssetPath(guids[0]); 
                        sentenceData.FontAsset = AssetDatabase.LoadAssetAtPath<TMP_FontAsset>(path); 
                    } 
                    else { 
                        Debug.LogWarning($"VisualTextDocument: Não foi possível encontrar a fonte TMP_FontAsset com o nome '{fontName}'."); 
                    } 
                    #endif 
                    sentenceData.Text = sentenceData.Text.Replace(fontMatch.Value, "").Trim(); 
                }

                // Parse para <typewriter use="false" delay="0.02" p_delay="0.1" p_pause="false" />
                string typewriterPattern = @"<typewriter\s*(.*?)\s*/?>";
                Match typewriterMatch = Regex.Match(sentenceData.Text, typewriterPattern, RegexOptions.IgnoreCase);
                if (typewriterMatch.Success)
                {
                    string attributes = typewriterMatch.Groups[1].Value;

                    // Parse 'use'
                    Match useMatch = Regex.Match(attributes, @"use=[""'](true|false)[""']", RegexOptions.IgnoreCase);
                    if (useMatch.Success) sentenceData.UseTypewriter = bool.Parse(useMatch.Groups[1].Value);

                    // Parse 'delay'
                    Match delayMatch = Regex.Match(attributes, @"delay=[""'](.*?)[""']", RegexOptions.IgnoreCase);
                    if (delayMatch.Success && float.TryParse(delayMatch.Groups[1].Value, System.Globalization.NumberStyles.Any, System.Globalization.CultureInfo.InvariantCulture, out float delayValue))
                    {
                        sentenceData.DelayPerCharacter = delayValue;
                    }

                    // Parse 'p_delay' (punctuation delay)
                    Match pDelayMatch = Regex.Match(attributes, @"p_delay=[""'](.*?)[""']", RegexOptions.IgnoreCase);
                    if (pDelayMatch.Success && float.TryParse(pDelayMatch.Groups[1].Value, System.Globalization.NumberStyles.Any, System.Globalization.CultureInfo.InvariantCulture, out float pDelayValue))
                    {
                        sentenceData.PunctuationDelay = pDelayValue;
                    }

                    // Parse 'p_pause' (pause on punctuation)
                    Match pPauseMatch = Regex.Match(attributes, @"p_pause=[""'](true|false)[""']", RegexOptions.IgnoreCase);
                    if (pPauseMatch.Success) sentenceData.PauseOnPunctuation = bool.Parse(pPauseMatch.Groups[1].Value);

                        // Remove a tag do texto final
                        sentenceData.Text = sentenceData.Text.Replace(typewriterMatch.Value, "").Trim();
                    }

                    // Parse para <sound name="NomeDoAudioEffect">
                    string soundPattern = @"<sound name=[""'](.*?)[""']\s*/?>";
                    Match soundMatch = Regex.Match(sentenceData.Text, soundPattern, RegexOptions.IgnoreCase);
                    if (soundMatch.Success)
                    {
                        string soundName = soundMatch.Groups[1].Value;
    #if UNITY_EDITOR
                        string[] guids = AssetDatabase.FindAssets($"t:AudioEffect {soundName}");
                        if (guids.Length > 0) {
                            string path = AssetDatabase.GUIDToAssetPath(guids[0]);
                            sentenceData.TypewriterSound = AssetDatabase.LoadAssetAtPath<AudioEffect>(path);
                        } else {
                            Debug.LogWarning($"VisualTextDocument: Não foi possível encontrar o AudioEffect com o nome '{soundName}'.");
                        }
    #endif
                        sentenceData.Text = sentenceData.Text.Replace(soundMatch.Value, "").Trim();
                    }
            }
            phraseToParse.Sentences.Add(sentenceData);
        }

        // Atualiza o FullPhraseText para a pré-visualização no editor
        StringBuilder phraseTextBuilder = new StringBuilder();
        foreach (var sentence in phraseToParse.Sentences)
        {
            phraseTextBuilder.Append(sentence.Text).Append(" ");
        }
        phraseToParse.FullPhraseText = phraseTextBuilder.ToString().Trim();
    }

    /// <summary>
    /// Converte todas as sentenças configuradas em uma única string Rich Text para TextMeshPro.
    /// </summary>
    /// <param name="separator">O separador a ser usado entre as sentenças (ex: " " ou "\n").</param>
    /// <returns>Uma string formatada com Rich Text.</returns>
    public string ToRichText(string separator = " ")
    {
        StringBuilder richTextBuilder = new StringBuilder();
        for (int p = 0; p < Phrases.Count; p++)
        {
            PhraseData phrase = Phrases[p];
            for (int i = 0; i < phrase.Sentences.Count; i++)
            {
                SentenceDisplayData sentence = phrase.Sentences[i];
                string currentSentenceText;

                if (!sentence.IsFromBraces)
                {
                    // Para texto normal (fora das chaves), aplicamos os estilos definidos
                    string textToFormat = sentence.Text;

                    List<string> openingTags = new List<string>();
                    List<string> closingTags = new List<string>();

                    if (sentence.IsItalic)
                    {
                        openingTags.Add("<i>");
                        closingTags.Insert(0, "</i>");
                    }
                    if (sentence.IsBold)
                    {
                        openingTags.Add("<b>");
                        closingTags.Insert(0, "</b>");
                    }

                    openingTags.Add($"<size={sentence.FontSize}pt>");
                    closingTags.Insert(0, "</size>");

                    openingTags.Add($"<color=#{ColorUtility.ToHtmlStringRGB(sentence.TextColor)}>");
                    closingTags.Insert(0, "</color>");

                    currentSentenceText = string.Concat(string.Join("", openingTags), textToFormat, string.Join("", closingTags));
                }
                else
                {
                    // Para texto de dentro das chaves, assumimos que ele já contém as tags.
                    currentSentenceText = sentence.Text;
                }

                richTextBuilder.Append(currentSentenceText);

                if (i < phrase.Sentences.Count - 1)
                {
                    richTextBuilder.Append(separator);
                }
            }
            if (p < Phrases.Count - 1)
            {
                richTextBuilder.Append(separator);
            }
        }
        return richTextBuilder.ToString();
    }

    /// <summary>
    /// Permite configurar manualmente uma sentença específica dentro de uma frase.
    /// </summary>
    public void ConfigureSentence(int phraseIndex, int sentenceIndex, System.Action<SentenceDisplayData> configureAction)
    {
        if (phraseIndex >= 0 && phraseIndex < Phrases.Count &&
            sentenceIndex >= 0 && sentenceIndex < Phrases[phraseIndex].Sentences.Count)
        {
            configureAction?.Invoke(Phrases[phraseIndex].Sentences[sentenceIndex]);
        }
        else
        {
            Debug.LogWarning($"VisualTextDocument: Índices de frase ({phraseIndex}) ou sentença ({sentenceIndex}) fora dos limites.");
        }
    }
}

[CustomEditor(typeof(VisualTextDocument))]
public class VisualTextDocumentEditor : Editor
{

    public VisualTextDocument document;
    
    public override void OnInspectorGUI()
    {
        DrawDefaultInspector();

        if(document == null) document = (VisualTextDocument)target;

        if (GUILayout.Button("Open in Editor Window"))
        {
            VisualTextDocumentEditorWindow.ShowWindow(document); // Chama a nossa nova janela
        }

        if (GUILayout.Button("Parse Text"))
        {
            document.ParseAndConfigureText(document.fullText);
        }
    }
}