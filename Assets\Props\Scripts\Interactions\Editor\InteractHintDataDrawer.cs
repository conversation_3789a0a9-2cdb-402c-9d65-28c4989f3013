using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

[CustomPropertyDrawer(typeof(InteractHintData))]
public class InteractHintDataDrawer : PropertyDrawer
{
    private static Dictionary<string, bool> _foldoutStates = new Dictionary<string, bool>();
    
    public override void OnGUI(Rect position, SerializedProperty property, GUIContent label)
    {
        EditorGUI.BeginProperty(position, label, property);
        
        string propertyPath = property.propertyPath;
        if (!_foldoutStates.ContainsKey(propertyPath))
            _foldoutStates[propertyPath] = false;
        
        // Calculate positions
        float lineHeight = EditorGUIUtility.singleLineHeight;
        float spacing = EditorGUIUtility.standardVerticalSpacing;
        Rect currentRect = new Rect(position.x, position.y, position.width, lineHeight);
        
        // Main foldout
        _foldoutStates[propertyPath] = EditorGUI.Foldout(currentRect, _foldoutStates[propertyPath], label, true);
        currentRect.y += lineHeight + spacing;
        
        if (_foldoutStates[propertyPath])
        {
            EditorGUI.indentLevel++;
            
            // Basic properties
            var interactionTextProp = property.FindPropertyRelative("InteractionText");
            var interactionSpriteProp = property.FindPropertyRelative("InteractionSprite");
            var useAnimatedTextProp = property.FindPropertyRelative("UseAnimatedText");
            
            EditorGUI.PropertyField(currentRect, interactionTextProp);
            currentRect.y += lineHeight + spacing;
            
            EditorGUI.PropertyField(currentRect, interactionSpriteProp);
            currentRect.y += lineHeight + spacing;
            
            EditorGUI.PropertyField(currentRect, useAnimatedTextProp);
            currentRect.y += lineHeight + spacing;
            
            // Animated text configuration (only show if UseAnimatedText is true)
            if (useAnimatedTextProp.boolValue)
            {
                currentRect.y += spacing; // Extra space before animated section
                
                // Animated Text Document
                var animatedTextDocumentProp = property.FindPropertyRelative("AnimatedTextDocument");
                EditorGUI.PropertyField(currentRect, animatedTextDocumentProp);
                currentRect.y += lineHeight + spacing;
                
                // Show configuration options only if no document is assigned
                if (animatedTextDocumentProp.objectReferenceValue == null)
                {
                    // Help box
                    var helpRect = new Rect(currentRect.x, currentRect.y, currentRect.width, lineHeight * 2);
                    EditorGUI.HelpBox(helpRect, "Configure animated text settings below, or assign a VisualTextDocument above for advanced configuration.", MessageType.Info);
                    currentRect.y += lineHeight * 2 + spacing;
                    
                    // Text Effects
                    var textEffectsProp = property.FindPropertyRelative("TextEffects");
                    EditorGUI.PropertyField(currentRect, textEffectsProp, true);
                    currentRect.y += EditorGUI.GetPropertyHeight(textEffectsProp, true) + spacing;
                    
                    // Typewriter settings
                    var useTypewriterProp = property.FindPropertyRelative("UseTypewriter");
                    EditorGUI.PropertyField(currentRect, useTypewriterProp);
                    currentRect.y += lineHeight + spacing;
                    
                    if (useTypewriterProp.boolValue)
                    {
                        var delayPerCharacterProp = property.FindPropertyRelative("DelayPerCharacter");
                        EditorGUI.PropertyField(currentRect, delayPerCharacterProp);
                        currentRect.y += lineHeight + spacing;
                    }
                    
                    // Visual settings
                    var textColorProp = property.FindPropertyRelative("TextColor");
                    var fontSizeProp = property.FindPropertyRelative("FontSize");
                    
                    EditorGUI.PropertyField(currentRect, textColorProp);
                    currentRect.y += lineHeight + spacing;
                    
                    EditorGUI.PropertyField(currentRect, fontSizeProp);
                    currentRect.y += lineHeight + spacing;
                }
                else
                {
                    // Show info about using the document
                    var helpRect = new Rect(currentRect.x, currentRect.y, currentRect.width, lineHeight);
                    EditorGUI.HelpBox(helpRect, "Using assigned VisualTextDocument. Text configuration below will be ignored.", MessageType.Info);
                    currentRect.y += lineHeight + spacing;
                }
            }
            
            EditorGUI.indentLevel--;
        }
        
        EditorGUI.EndProperty();
    }
    
    public override float GetPropertyHeight(SerializedProperty property, GUIContent label)
    {
        string propertyPath = property.propertyPath;
        if (!_foldoutStates.ContainsKey(propertyPath) || !_foldoutStates[propertyPath])
        {
            return EditorGUIUtility.singleLineHeight;
        }
        
        float height = EditorGUIUtility.singleLineHeight; // Main foldout
        float lineHeight = EditorGUIUtility.singleLineHeight;
        float spacing = EditorGUIUtility.standardVerticalSpacing;
        
        // Basic properties (3 lines)
        height += (lineHeight + spacing) * 3;
        
        var useAnimatedTextProp = property.FindPropertyRelative("UseAnimatedText");
        if (useAnimatedTextProp.boolValue)
        {
            height += spacing; // Extra space
            height += lineHeight + spacing; // AnimatedTextDocument
            
            var animatedTextDocumentProp = property.FindPropertyRelative("AnimatedTextDocument");
            if (animatedTextDocumentProp.objectReferenceValue == null)
            {
                height += lineHeight * 2 + spacing; // Help box
                
                // Text Effects (variable height)
                var textEffectsProp = property.FindPropertyRelative("TextEffects");
                height += EditorGUI.GetPropertyHeight(textEffectsProp, true) + spacing;
                
                height += lineHeight + spacing; // UseTypewriter
                
                var useTypewriterProp = property.FindPropertyRelative("UseTypewriter");
                if (useTypewriterProp.boolValue)
                {
                    height += lineHeight + spacing; // DelayPerCharacter
                }
                
                height += (lineHeight + spacing) * 2; // TextColor and FontSize
            }
            else
            {
                height += lineHeight + spacing; // Help box for document
            }
        }
        
        return height;
    }
}
