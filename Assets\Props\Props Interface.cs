using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public interface IHighlightable
{
    
}

public interface IViewDetection : IFocusDetection, IFieldOfViewDetection {}

public interface IFocusDetection : IGameEntityInteracts
{
    public void StartView(IGameEntity sender);
    public void ContinueView(IGameEntity sender);
    public void EndView(IGameEntity sender);
}

public interface IFieldOfViewDetection : IGameEntityInteracts
{
    public void EnterFieldOfView(IGameEntity sender);
    public void StayInFieldOfView(IGameEntity sender);
    public void ExitFieldOfView(IGameEntity sender);
}

public interface IInteractable : IFocusDetection
{
    /// <summary>
    /// Event raised when the player enters the interact distance of this object.
    /// </summary>
    public virtual void OnInteractDistanceEnter(IGameEntity entity) { }

    /// <summary>
    /// Event raised when the player stays within the interact distance of this object.
    /// </summary>
    public virtual void OnInteractDistanceExit(IGameEntity entity) { }

    /// <summary>
    /// Event raised when the player starts interacting with this object.
    /// </summary>
    public virtual void OnStartInteract(IGameEntity sender) { }

    /// <summary>
    /// Event raised when the player stops interacting with this object.
    /// </summary>
    public virtual void OnStopInteract(IGameEntity sender) { }
    
    public InteractHintData interactHintData { get; set;}
    public virtual InteractHintData GetInteractHint() { return interactHintData; }
}

public enum InteractionState
{
    None,       // Not interacting or idle
    Started,    // Interaction has begun
    InProgress, // Interaction is ongoing (e.g., progress bar filling)
    Completed,  // Interaction finished successfully
    Canceled    // Interaction was interrupted or failed by the object itself
}

