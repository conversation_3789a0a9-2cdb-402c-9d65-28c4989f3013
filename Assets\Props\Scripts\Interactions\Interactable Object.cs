using UnityEngine;
using UnityEngine.InputSystem;
using UnityEditor;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;

[System.Serializable]
public class InteractHint
{
    [Header("Crosshair Settings")]
    [Tooltip("Sprite and Text to display on the crosshair when this object is focused.")]
    public InteractHintData interactionHint;

    public bool HasCondition;
    public List<BaseConditionSO> conditionSO;

    public bool CheckConditions()
    {
        if(HasCondition && conditionSO != null && conditionSO.Count > 0)
        {
            bool allConditionsMet = conditionSO.All(condition => condition.EvaluateCondition());
            if(!allConditionsMet) return false;
        }
        return true;
    }
}


public abstract class InteractableObject : MonoBehaviour, IInteractable, IFieldOfViewDetection
{
    // List of active IGameEntity like player or npc
    protected readonly HashSet<IGameEntity> activeHandlers = new HashSet<IGameEntity>();

    [<PERSON><PERSON>("Highlight Settings"), <PERSON><PERSON><PERSON>("Highlight settings for this interactable object")]
    public InteractHint interactHint;

    public virtual void RegisterInputs(PlayerInputHandler inputHandler) { }
    public virtual void UnregisterInputs(PlayerInputHandler inputHandler) { }

    /// <summary>
    /// Event raised when an IGameEntity starts interacting with this object.
    /// Default implementation sets the state to Started.
    /// </summary>
    public virtual void OnStartInteract(IGameEntity sender)
    {

    }

    /// <summary>
    /// Event raised when an IGameEntity stops interacting with this object.
    /// Default implementation sets the state to Canceled if currently Started or InProgress.
    /// </summary>
    public virtual void OnStopInteract(IGameEntity sender)
    {

    }

    /// <summary>
    /// Event raised when the state of an interaction with this object changes.
    /// Provides the interactable object, its new state, and a progress value (0.0 to 1.0).
    /// </summary>
    public virtual event Action<IInteractable, InteractionState, float> OnInteractionStateChanged;

    /// <summary>
    /// Estimated duration of the interaction in seconds. Override in derived classes.
    /// </summary>
    public virtual float InteractionDuration => 0f; // Default to 0, meaning instant or not applicable

    /// <summary>
    /// Event raised when an IGameEntity enters the interaction distance of this object.
    /// </summary>
    public virtual void OnInteractDistanceEnter(IGameEntity entity)
    {
        Debug.Log($"InteractableObject.OnInteractDistanceEnter called on {gameObject.name} by {entity?.GetType().Name}");
        if (!activeHandlers.Contains(entity)) activeHandlers.Add(entity);
        if (entity is IPlayer player)
        {
            PlayerInputHandler inputHandler = player.GetInputHandler();
            if (inputHandler != null) RegisterInputs(inputHandler);
        }
    }

    /// <summary>
    /// Event raised when an IGameEntity exits the interaction distance of this object.
    /// </summary>
    public virtual void OnInteractDistanceExit(IGameEntity entity)
    {
        Debug.Log($"InteractableObject.OnInteractDistanceExit called on {gameObject.name} by {entity?.GetType().Name}");
        if (activeHandlers.Contains(entity)) activeHandlers.Remove(entity);
        if (entity is IPlayer player)
        {
            PlayerInputHandler inputHandler = player.GetInputHandler();
            if (inputHandler != null) UnregisterInputs(inputHandler);
        }
    }

    public InteractHintData interactHintData { get; set;}
    public virtual InteractHintData GetInteractHint() 
    {  
        if(interactHint != null && interactHint.interactionHint != null)
        {
            if(interactHint.CheckConditions())
            {
                return interactHint.interactionHint;
            }
        }
        return null;
    }

    public virtual void StartView(IGameEntity sender) { }
    public virtual void ContinueView(IGameEntity sender) { }
    public virtual void EndView(IGameEntity sender) { }

    public virtual void EnterFieldOfView(IGameEntity sender) { }
    public virtual void StayInFieldOfView(IGameEntity sender) { }
    public virtual void ExitFieldOfView(IGameEntity sender) { }

    public virtual void OnDestroy()
    {
        foreach (var entity in activeHandlers)
        {
            if (entity is IPlayer player)
            {
                PlayerInputHandler inputHandler = player.GetInputHandler();
                if (inputHandler != null) UnregisterInputs(inputHandler);
            }
        }
        activeHandlers.Clear();
    }
}
