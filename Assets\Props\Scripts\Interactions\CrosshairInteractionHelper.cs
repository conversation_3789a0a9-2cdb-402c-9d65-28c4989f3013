using UnityEngine;

[System.Serializable]
public class CrosshairSettings
{
    [<PERSON><PERSON>("Crosshair Visual")]
    [Toolt<PERSON>("Sprite personalizado para o crosshair. Se null, usa o sprite padrão de interação")]
    public Sprite customCrosshairSprite;
    
    [<PERSON>er("Crosshair Text")]
    [Tooltip("Texto a ser exibido próximo ao crosshair")]
    public string interactionText = "Pressione E para interagir";
    
    [Header("Conditions")]
    [<PERSON><PERSON><PERSON>("Se verdadeiro, o crosshair só aparecerá se as condições forem atendidas")]
    public bool hasConditions = false;
    
    [Tooltip("Lista de condições que devem ser atendidas")]
    public BaseConditionSO[] conditions;
}

/// <summary>
/// Componente helper para facilitar a configuração de crosshair em objetos interagíveis
/// </summary>
public class CrosshairInteractionHelper : MonoBehaviour
{
    [Header("Crosshair Configuration")]
    public CrosshairSettings crosshairSettings;
    
    void Start()
    {
        // Aplica as configurações ao InteractableObject
        InteractableObject interactable = GetComponent<InteractableObject>();
        if (interactable != null)
        {
            ApplyCrosshairSettings(interactable);
        }
        else
        {
            Debug.LogWarning($"CrosshairInteractionHelper em {gameObject.name} não encontrou um InteractableObject!");
        }
    }
    
    private void ApplyCrosshairSettings(InteractableObject interactable)
    {
        // Configura InteractHintData
        if (interactable.interactHint == null)
        {
            interactable.interactHint.interactionHint = new InteractHintData();
        }
        
        interactable.interactHint.interactionHint.InteractionText = crosshairSettings.interactionText;
        interactable.interactHint.interactionHint.InteractionSprite = crosshairSettings.customCrosshairSprite;
        
        // Configura InteractHint
        if (interactable.interactHint == null)
        {
            interactable.interactHint.interactionHint = new InteractHintData();
        }
        
        interactable.interactHint.interactionHint.InteractionText = crosshairSettings.interactionText;
        interactable.interactHint.interactionHint.InteractionSprite = crosshairSettings.customCrosshairSprite;
        interactable.interactHint.HasCondition = crosshairSettings.hasConditions;
        
        if (crosshairSettings.hasConditions && crosshairSettings.conditions != null)
        {
            interactable.interactHint.conditionSO = new System.Collections.Generic.List<BaseConditionSO>(crosshairSettings.conditions);
        }
    }
    
    /// <summary>
    /// Atualiza as configurações do crosshair em runtime
    /// </summary>
    public void UpdateCrosshairSettings()
    {
        InteractableObject interactable = GetComponent<InteractableObject>();
        if (interactable != null)
        {
            ApplyCrosshairSettings(interactable);
        }
    }
    
    /// <summary>
    /// Muda o texto do crosshair em runtime
    /// </summary>
    public void SetInteractionText(string newText)
    {
        crosshairSettings.interactionText = newText;
        UpdateCrosshairSettings();
    }
    
    /// <summary>
    /// Muda o sprite do crosshair em runtime
    /// </summary>
    public void SetCrosshairSprite(Sprite newSprite)
    {
        crosshairSettings.customCrosshairSprite = newSprite;
        UpdateCrosshairSettings();
    }
}