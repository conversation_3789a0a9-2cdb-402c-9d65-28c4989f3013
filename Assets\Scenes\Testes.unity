%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!29 &1
OcclusionCullingSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_OcclusionBakeSettings:
    smallestOccluder: 5
    smallestHole: 0.25
    backfaceThreshold: 100
  m_SceneGUID: 00000000000000000000000000000000
  m_OcclusionCullingData: {fileID: 0}
--- !u!104 &2
RenderSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 10
  m_Fog: 0
  m_FogColor: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  m_FogMode: 3
  m_FogDensity: 0.01
  m_LinearFogStart: 0
  m_LinearFogEnd: 300
  m_AmbientSkyColor: {r: 0.212, g: 0.227, b: 0.259, a: 1}
  m_AmbientEquatorColor: {r: 0.114, g: 0.125, b: 0.133, a: 1}
  m_AmbientGroundColor: {r: 0.047, g: 0.043, b: 0.035, a: 1}
  m_AmbientIntensity: 1
  m_AmbientMode: 0
  m_SubtractiveShadowColor: {r: 0.42, g: 0.478, b: 0.627, a: 1}
  m_SkyboxMaterial: {fileID: 10304, guid: 0000000000000000f000000000000000, type: 0}
  m_HaloStrength: 0.5
  m_FlareStrength: 1
  m_FlareFadeSpeed: 3
  m_HaloTexture: {fileID: 0}
  m_SpotCookie: {fileID: 10001, guid: 0000000000000000e000000000000000, type: 0}
  m_DefaultReflectionMode: 0
  m_DefaultReflectionResolution: 128
  m_ReflectionBounces: 1
  m_ReflectionIntensity: 1
  m_CustomReflection: {fileID: 0}
  m_Sun: {fileID: 0}
  m_UseRadianceAmbientProbe: 0
--- !u!157 &3
LightmapSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 13
  m_BakeOnSceneLoad: 0
  m_GISettings:
    serializedVersion: 2
    m_BounceScale: 1
    m_IndirectOutputScale: 1
    m_AlbedoBoost: 1
    m_EnvironmentLightingMode: 0
    m_EnableBakedLightmaps: 1
    m_EnableRealtimeLightmaps: 0
  m_LightmapEditorSettings:
    serializedVersion: 12
    m_Resolution: 2
    m_BakeResolution: 40
    m_AtlasSize: 1024
    m_AO: 0
    m_AOMaxDistance: 1
    m_CompAOExponent: 1
    m_CompAOExponentDirect: 0
    m_ExtractAmbientOcclusion: 0
    m_Padding: 2
    m_LightmapParameters: {fileID: 0}
    m_LightmapsBakeMode: 1
    m_TextureCompression: 1
    m_ReflectionCompression: 2
    m_MixedBakeMode: 2
    m_BakeBackend: 1
    m_PVRSampling: 1
    m_PVRDirectSampleCount: 32
    m_PVRSampleCount: 512
    m_PVRBounces: 2
    m_PVREnvironmentSampleCount: 256
    m_PVREnvironmentReferencePointCount: 2048
    m_PVRFilteringMode: 1
    m_PVRDenoiserTypeDirect: 1
    m_PVRDenoiserTypeIndirect: 1
    m_PVRDenoiserTypeAO: 1
    m_PVRFilterTypeDirect: 0
    m_PVRFilterTypeIndirect: 0
    m_PVRFilterTypeAO: 0
    m_PVREnvironmentMIS: 1
    m_PVRCulling: 1
    m_PVRFilteringGaussRadiusDirect: 1
    m_PVRFilteringGaussRadiusIndirect: 5
    m_PVRFilteringGaussRadiusAO: 2
    m_PVRFilteringAtrousPositionSigmaDirect: 0.5
    m_PVRFilteringAtrousPositionSigmaIndirect: 2
    m_PVRFilteringAtrousPositionSigmaAO: 1
    m_ExportTrainingData: 0
    m_TrainingDataDestination: TrainingData
    m_LightProbeSampleCountMultiplier: 4
  m_LightingDataAsset: {fileID: 20201, guid: 0000000000000000f000000000000000, type: 0}
  m_LightingSettings: {fileID: 0}
--- !u!196 &4
NavMeshSettings:
  serializedVersion: 2
  m_ObjectHideFlags: 0
  m_BuildSettings:
    serializedVersion: 3
    agentTypeID: 0
    agentRadius: 0.5
    agentHeight: 2
    agentSlope: 45
    agentClimb: 0.4
    ledgeDropHeight: 0
    maxJumpAcrossDistance: 0
    minRegionArea: 2
    manualCellSize: 0
    cellSize: 0.16666667
    manualTileSize: 0
    tileSize: 256
    buildHeightMesh: 0
    maxJobWorkers: 0
    preserveTilesOutsideBounds: 0
    debug:
      m_Flags: 0
  m_NavMeshData: {fileID: 0}
--- !u!1 &6576948
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6576953}
  - component: {fileID: 6576952}
  - component: {fileID: 6576951}
  - component: {fileID: 6576950}
  - component: {fileID: 6576949}
  - component: {fileID: 6576955}
  - component: {fileID: 6576954}
  m_Layer: 5
  m_Name: Canvas
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!114 &6576949
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6576948}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 362604d177875be4cb9bd0de39d312f6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  modelInfo:
    prefab: {fileID: 0}
    defaultRotation: {x: -90, y: 90, z: 0}
    intrinsicForward: {x: 0, y: 0, z: 0}
    centerOffset: {x: -0.08583498, y: 0.3284172, z: 0.00000002980232}
  model: {fileID: 0}
  rect: {fileID: 0}
  threshold: 0.9
--- !u!114 &6576950
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6576948}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc42784cf147c0c48a680349fa168899, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IgnoreReversedGraphics: 1
  m_BlockingObjects: 0
  m_BlockingMask:
    serializedVersion: 2
    m_Bits: 4294967295
--- !u!114 &6576951
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6576948}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0cd44c1031e13a943bb63640046fad76, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UiScaleMode: 0
  m_ReferencePixelsPerUnit: 100
  m_ScaleFactor: 1
  m_ReferenceResolution: {x: 800, y: 600}
  m_ScreenMatchMode: 0
  m_MatchWidthOrHeight: 0
  m_PhysicalUnit: 3
  m_FallbackScreenDPI: 96
  m_DefaultSpriteDPI: 96
  m_DynamicPixelsPerUnit: 1
  m_PresetInfoIsWorld: 0
--- !u!223 &6576952
Canvas:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6576948}
  m_Enabled: 1
  serializedVersion: 3
  m_RenderMode: 0
  m_Camera: {fileID: 0}
  m_PlaneDistance: 100
  m_PixelPerfect: 0
  m_ReceivesEvents: 1
  m_OverrideSorting: 0
  m_OverridePixelPerfect: 0
  m_SortingBucketNormalizedSize: 0
  m_VertexColorAlwaysGammaSpace: 0
  m_AdditionalShaderChannelsFlag: 25
  m_UpdateRectTransformForStandalone: 0
  m_SortingLayerID: 0
  m_SortingOrder: 0
  m_TargetDisplay: 0
--- !u!224 &6576953
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6576948}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0, y: 0, z: 0}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1015810712}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0, y: 0}
--- !u!114 &6576954
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6576948}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9541d86e2fd84c1d9990edf0852d74ab, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: 
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_sharedMaterial: {fileID: 2180264, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4294967295
  m_fontColor: {r: 1, g: 1, b: 1, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 36
  m_fontSizeBase: 36
  m_fontWeight: 400
  m_enableAutoSizing: 0
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 1
  m_VerticalAlignment: 256
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 0
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 0
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  _SortingLayer: 0
  _SortingLayerID: 0
  _SortingOrder: 0
  m_hasFontAssetChanged: 0
  m_renderer: {fileID: 6576955}
  m_maskType: 0
--- !u!23 &6576955
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6576948}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2180264, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &26822128 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 5820757280525948126, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
  m_PrefabInstance: {fileID: 6149037546204482254}
  m_PrefabAsset: {fileID: 0}
--- !u!4 &26822133 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 9168499450705671705, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
  m_PrefabInstance: {fileID: 6149037546204482254}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &26822134
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 26822128}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 948f4100a11a5c24981795d21301da5c, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  volumeTrigger: {fileID: 26822133}
  volumeLayer:
    serializedVersion: 2
    m_Bits: 2
  stopNaNPropagation: 1
  finalBlitToCameraTarget: 0
  antialiasingMode: 0
  temporalAntialiasing:
    jitterSpread: 0.75
    sharpness: 0.25
    stationaryBlending: 0.95
    motionBlending: 0.85
  subpixelMorphologicalAntialiasing:
    quality: 2
  fastApproximateAntialiasing:
    fastMode: 0
    keepAlpha: 0
  fog:
    enabled: 1
    excludeSkybox: 1
  debugLayer:
    lightMeter:
      width: 512
      height: 256
      showCurves: 1
    histogram:
      width: 512
      height: 256
      channel: 3
    waveform:
      exposure: 0.12
      height: 256
    vectorscope:
      size: 256
      exposure: 0.12
    overlaySettings:
      linearDepth: 0
      motionColorIntensity: 4
      motionGridSize: 64
      colorBlindnessType: 0
      colorBlindnessStrength: 1
  m_Resources: {fileID: 11400000, guid: d82512f9c8e5d4a4d938b575d47f88d4, type: 2}
  m_ShowToolkit: 0
  m_ShowCustomSorter: 0
  breakBeforeColorGrading: 0
  m_BeforeTransparentBundles: []
  m_BeforeStackBundles: []
  m_AfterStackBundles: []
--- !u!20 &26822135 stripped
Camera:
  m_CorrespondingSourceObject: {fileID: 1828421452105551374, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
  m_PrefabInstance: {fileID: 6149037546204482254}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &38409045
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 38409046}
  - component: {fileID: 38409048}
  - component: {fileID: 38409047}
  m_Layer: 0
  m_Name: Icon
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &38409046
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 38409045}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2137849847}
  m_Father: {fileID: 261096444}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: -20, y: -20}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &38409047
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 38409045}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0, g: 1, b: 0.8949728, a: 0.45490196}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &38409048
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 38409045}
  m_CullTransparentMesh: 1
--- !u!1 &61628568
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 61628569}
  - component: {fileID: 61628571}
  - component: {fileID: 61628570}
  m_Layer: 0
  m_Name: Text (TMP)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &61628569
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 61628568}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 444281375}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 1, y: 0}
  m_AnchorMax: {x: 1, y: 0}
  m_AnchoredPosition: {x: -18.6604, y: 5.2325}
  m_SizeDelta: {x: 37.3207, y: 9.9206}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &61628570
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 61628568}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: 
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_sharedMaterial: {fileID: 2180264, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4278190335
  m_fontColor: {r: 1, g: 0, b: 0, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 36
  m_fontSizeBase: 36
  m_fontWeight: 400
  m_enableAutoSizing: 1
  m_fontSizeMin: 1
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 4
  m_VerticalAlignment: 1024
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 0
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!222 &61628571
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 61628568}
  m_CullTransparentMesh: 1
--- !u!1 &67746910
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 67746911}
  m_Layer: 4
  m_Name: Top Point
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &67746911
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 67746910}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0.506, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1606239611}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &82983280
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 82983281}
  m_Layer: 0
  m_Name: Spawn Item
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &82983281
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 82983280}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: -24}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1172402183}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1 &89946522
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 89946523}
  - component: {fileID: 89946525}
  - component: {fileID: 89946524}
  m_Layer: 5
  m_Name: Image
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &89946523
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 89946522}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 212244598}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 100, y: 100}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &89946524
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 89946522}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300000, guid: 2e87f40e398ec634785ff2ef83919203, type: 3}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &89946525
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 89946522}
  m_CullTransparentMesh: 1
--- !u!1 &110915052
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 110915053}
  - component: {fileID: 110915056}
  - component: {fileID: 110915055}
  - component: {fileID: 110915054}
  m_Layer: 0
  m_Name: Slot 9
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &110915053
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 110915052}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 545318121}
  - {fileID: 1492303155}
  - {fileID: 560479876}
  m_Father: {fileID: 687848106}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!225 &110915054
CanvasGroup:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 110915052}
  m_Enabled: 1
  m_Alpha: 1
  m_Interactable: 1
  m_BlocksRaycasts: 1
  m_IgnoreParentGroups: 0
--- !u!114 &110915055
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 110915052}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4e29b1a8efbd4b44bb3f3716e73f07ff, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.92993355, g: 1, b: 0, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 545318122}
  m_OnClick:
    m_PersistentCalls:
      m_Calls: []
--- !u!114 &110915056
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 110915052}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b2bd544dc8bd07147b60d76c0c13a6f3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemIconImage: {fileID: 0}
  backgroundImage: {fileID: 545318122}
  quantityText: {fileID: 457682853}
  parentUI: {fileID: 475421019}
  defaultBackgroundColor: {r: 1, g: 1, b: 1, a: 1}
  coveredBackgroundColor: {r: 0.8, g: 0.8, b: 0.8, a: 0.5}
  occupiedBackgroundColor: {r: 0.9, g: 0.9, b: 0.9, a: 1}
  slotIndexInInventory: 0
  CurrentModelPresenter: {fileID: 0}
  selectionHighlightImage: {fileID: 0}
--- !u!1 &117214858
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 117214859}
  m_Layer: 4
  m_Name: Bottom Point
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &117214859
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 117214858}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: -0.3969, z: 0.21}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1651308565}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &161268397
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 161268398}
  - component: {fileID: 161268401}
  - component: {fileID: 161268400}
  - component: {fileID: 161268399}
  m_Layer: 0
  m_Name: Slot 8
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &161268398
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 161268397}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1763709973}
  - {fileID: 204929908}
  - {fileID: 733408586}
  m_Father: {fileID: 687848106}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!225 &161268399
CanvasGroup:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 161268397}
  m_Enabled: 1
  m_Alpha: 1
  m_Interactable: 1
  m_BlocksRaycasts: 1
  m_IgnoreParentGroups: 0
--- !u!114 &161268400
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 161268397}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4e29b1a8efbd4b44bb3f3716e73f07ff, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.92993355, g: 1, b: 0, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 1763709974}
  m_OnClick:
    m_PersistentCalls:
      m_Calls: []
--- !u!114 &161268401
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 161268397}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b2bd544dc8bd07147b60d76c0c13a6f3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemIconImage: {fileID: 0}
  backgroundImage: {fileID: 1763709974}
  quantityText: {fileID: 1717622684}
  parentUI: {fileID: 475421019}
  defaultBackgroundColor: {r: 1, g: 1, b: 1, a: 1}
  coveredBackgroundColor: {r: 0.8, g: 0.8, b: 0.8, a: 0.5}
  occupiedBackgroundColor: {r: 0.9, g: 0.9, b: 0.9, a: 1}
  slotIndexInInventory: 0
  CurrentModelPresenter: {fileID: 0}
  selectionHighlightImage: {fileID: 0}
--- !u!1 &162193658
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 162193659}
  - component: {fileID: 162193661}
  - component: {fileID: 162193660}
  m_Layer: 0
  m_Name: Image
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &162193659
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 162193658}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1445181762}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &162193660
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 162193658}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &162193661
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 162193658}
  m_CullTransparentMesh: 1
--- !u!1 &168342584
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 168342585}
  m_Layer: 0
  m_Name: Spawn Item
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &168342585
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 168342584}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: -24}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1912301149}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1 &190675092
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 190675093}
  - component: {fileID: 190675095}
  - component: {fileID: 190675094}
  m_Layer: 0
  m_Name: Icon
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &190675093
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 190675092}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1993188518}
  m_Father: {fileID: 1912301149}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: -20, y: -20}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &190675094
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 190675092}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0, g: 1, b: 0.8949728, a: 0.45490196}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &190675095
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 190675092}
  m_CullTransparentMesh: 1
--- !u!1 &192052686
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 192052687}
  - component: {fileID: 192052689}
  - component: {fileID: 192052688}
  m_Layer: 0
  m_Name: Image
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &192052687
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 192052686}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1172402183}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &192052688
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 192052686}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &192052689
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 192052686}
  m_CullTransparentMesh: 1
--- !u!1 &204929907
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 204929908}
  m_Layer: 0
  m_Name: Spawn Item
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &204929908
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 204929907}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: -24}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 161268398}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1 &210829131
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 210829132}
  - component: {fileID: 210829134}
  - component: {fileID: 210829133}
  m_Layer: 0
  m_Name: Text (TMP)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &210829132
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 210829131}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 408831303}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 1, y: 0}
  m_AnchorMax: {x: 1, y: 0}
  m_AnchoredPosition: {x: -18.6604, y: 5.2325}
  m_SizeDelta: {x: 37.3207, y: 9.9206}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &210829133
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 210829131}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: 
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_sharedMaterial: {fileID: 2180264, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4278190335
  m_fontColor: {r: 1, g: 0, b: 0, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 36
  m_fontSizeBase: 36
  m_fontWeight: 400
  m_enableAutoSizing: 1
  m_fontSizeMin: 1
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 4
  m_VerticalAlignment: 1024
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 0
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!222 &210829134
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 210829131}
  m_CullTransparentMesh: 1
--- !u!224 &212244598 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 1612572572199675471, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
  m_PrefabInstance: {fileID: 3504528070976578228}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &231651484
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: *********}
  - component: {fileID: 231651488}
  - component: {fileID: 231651487}
  - component: {fileID: 231651486}
  m_Layer: 0
  m_Name: Slot 10
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &*********
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 231651484}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1556391161}
  - {fileID: 1709965264}
  - {fileID: *********}
  m_Father: {fileID: 687848106}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!225 &231651486
CanvasGroup:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 231651484}
  m_Enabled: 1
  m_Alpha: 1
  m_Interactable: 1
  m_BlocksRaycasts: 1
  m_IgnoreParentGroups: 0
--- !u!114 &231651487
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 231651484}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4e29b1a8efbd4b44bb3f3716e73f07ff, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.92993355, g: 1, b: 0, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 1556391162}
  m_OnClick:
    m_PersistentCalls:
      m_Calls: []
--- !u!114 &231651488
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 231651484}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b2bd544dc8bd07147b60d76c0c13a6f3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemIconImage: {fileID: 0}
  backgroundImage: {fileID: 1556391162}
  quantityText: {fileID: 658427058}
  parentUI: {fileID: 475421019}
  defaultBackgroundColor: {r: 1, g: 1, b: 1, a: 1}
  coveredBackgroundColor: {r: 0.8, g: 0.8, b: 0.8, a: 0.5}
  occupiedBackgroundColor: {r: 0.9, g: 0.9, b: 0.9, a: 1}
  slotIndexInInventory: 0
  CurrentModelPresenter: {fileID: 0}
  selectionHighlightImage: {fileID: 0}
--- !u!1 &249105722
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 249105723}
  - component: {fileID: 249105725}
  - component: {fileID: 249105724}
  m_Layer: 0
  m_Name: Text (TMP)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &249105723
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 249105722}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 296346163}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 1, y: 0}
  m_AnchorMax: {x: 1, y: 0}
  m_AnchoredPosition: {x: -18.6604, y: 5.2325}
  m_SizeDelta: {x: 37.3207, y: 9.9206}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &249105724
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 249105722}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: 
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_sharedMaterial: {fileID: 2180264, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4278190335
  m_fontColor: {r: 1, g: 0, b: 0, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 36
  m_fontSizeBase: 36
  m_fontWeight: 400
  m_enableAutoSizing: 1
  m_fontSizeMin: 1
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 4
  m_VerticalAlignment: 1024
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 0
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!222 &249105725
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 249105722}
  m_CullTransparentMesh: 1
--- !u!1 &261096443
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 261096444}
  - component: {fileID: 261096447}
  - component: {fileID: 261096446}
  - component: {fileID: 261096445}
  m_Layer: 0
  m_Name: Slot 18
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &261096444
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 261096443}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 387698082}
  - {fileID: 1547280087}
  - {fileID: 38409046}
  m_Father: {fileID: 687848106}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!225 &261096445
CanvasGroup:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 261096443}
  m_Enabled: 1
  m_Alpha: 1
  m_Interactable: 1
  m_BlocksRaycasts: 1
  m_IgnoreParentGroups: 0
--- !u!114 &261096446
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 261096443}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4e29b1a8efbd4b44bb3f3716e73f07ff, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.92993355, g: 1, b: 0, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 387698083}
  m_OnClick:
    m_PersistentCalls:
      m_Calls: []
--- !u!114 &261096447
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 261096443}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b2bd544dc8bd07147b60d76c0c13a6f3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemIconImage: {fileID: 0}
  backgroundImage: {fileID: 387698083}
  quantityText: {fileID: 2137849848}
  parentUI: {fileID: 475421019}
  defaultBackgroundColor: {r: 1, g: 1, b: 1, a: 1}
  coveredBackgroundColor: {r: 0.8, g: 0.8, b: 0.8, a: 0.5}
  occupiedBackgroundColor: {r: 0.9, g: 0.9, b: 0.9, a: 1}
  slotIndexInInventory: 0
  CurrentModelPresenter: {fileID: 0}
  selectionHighlightImage: {fileID: 0}
--- !u!1 &287183103 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 6631933611597660996, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
  m_PrefabInstance: {fileID: 6149037546204482254}
  m_PrefabAsset: {fileID: 0}
--- !u!4 &287183104 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 5909042491891516713, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
  m_PrefabInstance: {fileID: 6149037546204482254}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &287183105
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 287183103}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f9dfa5b682dcd46bda6128250e975f58, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Priority:
    Enabled: 1
    m_Value: 0
  OutputChannel: 1
  StandbyUpdate: 2
  m_StreamingVersion: 20241001
  m_LegacyPriority: 0
  Target:
    TrackingTarget: {fileID: 0}
    LookAtTarget: {fileID: 0}
    CustomLookAtTarget: 0
  Lens:
    FieldOfView: 40
    OrthographicSize: 10
    NearClipPlane: 0.1
    FarClipPlane: 100
    Dutch: 0
    ModeOverride: 0
    PhysicalProperties:
      GateFit: 2
      SensorSize: {x: 21.946, y: 16.002}
      LensShift: {x: 0, y: 0}
      FocusDistance: 10
      Iso: 200
      ShutterSpeed: 0.005
      Aperture: 16
      BladeCount: 5
      Curvature: {x: 2, y: 11}
      BarrelClipping: 0.25
      Anamorphism: 0
  BlendHint: 0
--- !u!114 &287183106
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 287183103}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78a34e49456453b4da8fb7f088af865a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  headBobs: []
  cameraBobOffset: {fileID: 287183104}
  positionOffset: {x: 0, y: 0, z: 0}
  IdleBob:
    duration: 0
    positionMax: {x: 0, y: 0.1, z: 0}
    positionMin: {x: 0, y: 0, z: 0}
    speed: 0.5
    rotationMax: {x: 0, y: 0, z: 0}
    rotationMin: {x: 0, y: 0, z: 0}
    rotationSpeed: 0
    phaseOffset: 0
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 1
        outSlope: 1
        tangentMode: 34
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 1
        tangentMode: 34
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    blendSpeed: 1
    noiseAmount: 0
    intensityMultiplier: 1
  WalkBob:
    duration: 0
    positionMax: {x: 0, y: 0.15, z: 0}
    positionMin: {x: 0, y: 0, z: 0}
    speed: 4
    rotationMax: {x: 0, y: 0, z: 0.3}
    rotationMin: {x: 0, y: 0, z: -0.3}
    rotationSpeed: 2
    phaseOffset: 0
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 1
        outSlope: 1
        tangentMode: 34
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 1
        tangentMode: 34
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    blendSpeed: 5
    noiseAmount: 0
    intensityMultiplier: 1
  RunBob:
    duration: 0
    positionMax: {x: 0, y: 0.3, z: 0}
    positionMin: {x: 0, y: -0.1, z: 0}
    speed: 6
    rotationMax: {x: 0, y: 0, z: 0.8}
    rotationMin: {x: 0, y: 0, z: -0.8}
    rotationSpeed: 3
    phaseOffset: 0
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    blendSpeed: 5
    noiseAmount: 0
    intensityMultiplier: 1
  smoothSpeed: 10
  overrideHeadBob: 0
  playerMovement: {fileID: 858718435}
--- !u!1 &293746991
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 293746992}
  - component: {fileID: 293746994}
  - component: {fileID: 293746993}
  m_Layer: 0
  m_Name: Text (TMP)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &293746992
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 293746991}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1583670868}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 1, y: 0}
  m_AnchorMax: {x: 1, y: 0}
  m_AnchoredPosition: {x: -18.6604, y: 5.2325}
  m_SizeDelta: {x: 37.3207, y: 9.9206}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &293746993
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 293746991}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: 
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_sharedMaterial: {fileID: 2180264, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4278190335
  m_fontColor: {r: 1, g: 0, b: 0, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 36
  m_fontSizeBase: 36
  m_fontWeight: 400
  m_enableAutoSizing: 1
  m_fontSizeMin: 1
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 4
  m_VerticalAlignment: 1024
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 0
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!222 &293746994
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 293746991}
  m_CullTransparentMesh: 1
--- !u!1 &296346162
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 296346163}
  - component: {fileID: 296346165}
  - component: {fileID: 296346164}
  m_Layer: 0
  m_Name: Icon
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &296346163
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 296346162}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 249105723}
  m_Father: {fileID: 2044205516}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: -20, y: -20}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &296346164
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 296346162}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0, g: 1, b: 0.8949728, a: 0.45490196}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &296346165
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 296346162}
  m_CullTransparentMesh: 1
--- !u!1 &316418720
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 316418724}
  - component: {fileID: 316418723}
  - component: {fileID: 316418722}
  - component: {fileID: 316418721}
  - component: {fileID: 316418725}
  m_Layer: 5
  m_Name: Text (TMP)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &316418721
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 316418720}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 53c75cf150ed1f34cb351cbfe57cbae7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  textComponent: {fileID: 316418722}
  audioSource: {fileID: 316418725}
  fastForwardTypewriterMultiplier: 5
  fastForwardAnimationMultiplier: 3
--- !u!114 &316418722
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 316418720}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: "<color=#FFFFFF><sicolor> <color=#FFFFFF><size=36pt>Segunda senten\xE7a
    que ser\xE1 apagada.</size></color> "
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_sharedMaterial: {fileID: 2180264, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4294967295
  m_fontColor: {r: 1, g: 1, b: 1, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 36
  m_fontSizeBase: 36
  m_fontWeight: 400
  m_enableAutoSizing: 0
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 1
  m_VerticalAlignment: 512
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 0
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!222 &316418723
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 316418720}
  m_CullTransparentMesh: 1
--- !u!224 &316418724
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 316418720}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1042441660}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 956.3594, y: 296.5343}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!82 &316418725
AudioSource:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 316418720}
  m_Enabled: 1
  serializedVersion: 4
  OutputAudioMixerGroup: {fileID: 0}
  m_audioClip: {fileID: 0}
  m_Resource: {fileID: 0}
  m_PlayOnAwake: 1
  m_Volume: 1
  m_Pitch: 1
  Loop: 0
  Mute: 0
  Spatialize: 0
  SpatializePostEffects: 0
  Priority: 128
  DopplerLevel: 1
  MinDistance: 1
  MaxDistance: 500
  Pan2D: 0
  rolloffMode: 0
  BypassEffects: 0
  BypassListenerEffects: 0
  BypassReverbZones: 0
  rolloffCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  panLevelCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  spreadCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  reverbZoneMixCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
--- !u!1 &319518061
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 319518062}
  - component: {fileID: 319518064}
  - component: {fileID: 319518063}
  m_Layer: 0
  m_Name: Text (TMP)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &319518062
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 319518061}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1892822203}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 1, y: 0}
  m_AnchorMax: {x: 1, y: 0}
  m_AnchoredPosition: {x: -18.6604, y: 5.2325}
  m_SizeDelta: {x: 37.3207, y: 9.9206}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &319518063
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 319518061}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: 
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_sharedMaterial: {fileID: 2180264, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4278190335
  m_fontColor: {r: 1, g: 0, b: 0, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 36
  m_fontSizeBase: 36
  m_fontWeight: 400
  m_enableAutoSizing: 1
  m_fontSizeMin: 1
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 4
  m_VerticalAlignment: 1024
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 0
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!222 &319518064
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 319518061}
  m_CullTransparentMesh: 1
--- !u!1 &325580531
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 325580535}
  - component: {fileID: 325580534}
  - component: {fileID: 325580533}
  - component: {fileID: 325580532}
  m_Layer: 5
  m_Name: Bag
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!65 &325580532
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 325580531}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 0
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &325580533
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 325580531}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 31321ba15b8f8eb4c954353edc038b1d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &325580534
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 325580531}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!4 &325580535
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 325580531}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0.63, y: 0, z: 1.66}
  m_LocalScale: {x: 1, y: 1.27, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 212244598}
  m_Father: {fileID: 427868379}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &346950622
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 346950623}
  - component: {fileID: 346950625}
  - component: {fileID: 346950624}
  m_Layer: 0
  m_Name: Panel
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &346950623
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 346950622}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 475421021}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &346950624
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 346950622}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 0.392}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300000, guid: 2387f86f5c0a88f4ba454a165d72e8fc, type: 3}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &346950625
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 346950622}
  m_CullTransparentMesh: 1
--- !u!1 &358398578
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 358398579}
  - component: {fileID: 358398581}
  - component: {fileID: 358398580}
  m_Layer: 0
  m_Name: Text (TMP)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &358398579
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 358398578}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 780809930}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 1, y: 0}
  m_AnchorMax: {x: 1, y: 0}
  m_AnchoredPosition: {x: -18.6604, y: 5.2325}
  m_SizeDelta: {x: 37.3207, y: 9.9206}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &358398580
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 358398578}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: 
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_sharedMaterial: {fileID: 2180264, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4278190335
  m_fontColor: {r: 1, g: 0, b: 0, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 36
  m_fontSizeBase: 36
  m_fontWeight: 400
  m_enableAutoSizing: 1
  m_fontSizeMin: 1
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 4
  m_VerticalAlignment: 1024
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 0
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!222 &358398581
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 358398578}
  m_CullTransparentMesh: 1
--- !u!1 &365720263
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 365720264}
  - component: {fileID: 365720265}
  - component: {fileID: 365720266}
  m_Layer: 5
  m_Name: Progress Text
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &365720264
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 365720263}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 910774343}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 1, y: 1}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: -133.69, y: -97.109985}
  m_SizeDelta: {x: 200, y: 50}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &365720265
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 365720263}
  m_CullTransparentMesh: 1
--- !u!114 &365720266
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 365720263}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: New Text
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_sharedMaterial: {fileID: 2180264, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4294967295
  m_fontColor: {r: 1, g: 1, b: 1, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 36
  m_fontSizeBase: 36
  m_fontWeight: 400
  m_enableAutoSizing: 0
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 1
  m_VerticalAlignment: 256
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 0
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!1 &387698081
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 387698082}
  - component: {fileID: 387698084}
  - component: {fileID: 387698083}
  m_Layer: 0
  m_Name: Image
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &387698082
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 387698081}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 261096444}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &387698083
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 387698081}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &387698084
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 387698081}
  m_CullTransparentMesh: 1
--- !u!1 &391214795
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 391214796}
  - component: {fileID: 391214799}
  - component: {fileID: 391214798}
  - component: {fileID: 391214797}
  m_Layer: 0
  m_Name: Slot 12
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &391214796
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 391214795}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1980274882}
  - {fileID: 400622019}
  - {fileID: 781639908}
  m_Father: {fileID: 687848106}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!225 &391214797
CanvasGroup:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 391214795}
  m_Enabled: 1
  m_Alpha: 1
  m_Interactable: 1
  m_BlocksRaycasts: 1
  m_IgnoreParentGroups: 0
--- !u!114 &391214798
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 391214795}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4e29b1a8efbd4b44bb3f3716e73f07ff, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.92993355, g: 1, b: 0, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 1980274883}
  m_OnClick:
    m_PersistentCalls:
      m_Calls: []
--- !u!114 &391214799
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 391214795}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b2bd544dc8bd07147b60d76c0c13a6f3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemIconImage: {fileID: 0}
  backgroundImage: {fileID: 1980274883}
  quantityText: {fileID: 666423301}
  parentUI: {fileID: 475421019}
  defaultBackgroundColor: {r: 1, g: 1, b: 1, a: 1}
  coveredBackgroundColor: {r: 0.8, g: 0.8, b: 0.8, a: 0.5}
  occupiedBackgroundColor: {r: 0.9, g: 0.9, b: 0.9, a: 1}
  slotIndexInInventory: 0
  CurrentModelPresenter: {fileID: 0}
  selectionHighlightImage: {fileID: 0}
--- !u!1 &392111198
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 392111199}
  - component: {fileID: 392111201}
  - component: {fileID: 392111200}
  m_Layer: 0
  m_Name: Image
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &392111199
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 392111198}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2006396558}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &392111200
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 392111198}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &392111201
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 392111198}
  m_CullTransparentMesh: 1
--- !u!1 &400622018
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 400622019}
  m_Layer: 0
  m_Name: Spawn Item
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &400622019
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 400622018}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: -24}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 391214796}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1 &407994734
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 407994735}
  m_Layer: 0
  m_Name: Spawn Item
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &407994735
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 407994734}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: -24}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 971408196}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1 &408831302
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 408831303}
  - component: {fileID: 408831305}
  - component: {fileID: 408831304}
  m_Layer: 0
  m_Name: Icon
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &408831303
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 408831302}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 210829132}
  m_Father: {fileID: 763895328}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: -20, y: -20}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &408831304
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 408831302}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0, g: 1, b: 0.8949728, a: 0.45490196}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &408831305
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 408831302}
  m_CullTransparentMesh: 1
--- !u!1 &410087039
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 410087041}
  - component: {fileID: 410087040}
  - component: {fileID: 410087042}
  m_Layer: 0
  m_Name: Directional Light
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!108 &410087040
Light:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 410087039}
  m_Enabled: 1
  serializedVersion: 11
  m_Type: 1
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_Intensity: 2
  m_Range: 10
  m_SpotAngle: 30
  m_InnerSpotAngle: 21.80208
  m_CookieSize: 10
  m_Shadows:
    m_Type: 2
    m_Resolution: -1
    m_CustomResolution: -1
    m_Strength: 1
    m_Bias: 0.05
    m_NormalBias: 0.4
    m_NearPlane: 0.2
    m_CullingMatrixOverride:
      e00: 1
      e01: 0
      e02: 0
      e03: 0
      e10: 0
      e11: 1
      e12: 0
      e13: 0
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
    m_UseCullingMatrixOverride: 0
  m_Cookie: {fileID: 0}
  m_DrawHalo: 0
  m_Flare: {fileID: 0}
  m_RenderMode: 0
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingLayerMask: 1
  m_Lightmapping: 4
  m_LightShadowCasterMode: 0
  m_AreaSize: {x: 1, y: 1}
  m_BounceIntensity: 1
  m_ColorTemperature: 5000
  m_UseColorTemperature: 1
  m_BoundingSphereOverride: {x: 0, y: 0, z: 0, w: 0}
  m_UseBoundingSphereOverride: 0
  m_UseViewFrustumForShadowCasterCull: 1
  m_ForceVisible: 0
  m_ShadowRadius: 0
  m_ShadowAngle: 0
  m_LightUnit: 1
  m_LuxAtDistance: 1
  m_EnableSpotReflector: 1
--- !u!4 &410087041
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 410087039}
  serializedVersion: 2
  m_LocalRotation: {x: 0.40821788, y: -0.23456968, z: 0.10938163, w: 0.8754261}
  m_LocalPosition: {x: 0, y: 3, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 50, y: -30, z: 0}
--- !u!114 &410087042
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 410087039}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 474bcb49853aa07438625e644c072ee6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Version: 3
  m_UsePipelineSettings: 1
  m_AdditionalLightsShadowResolutionTier: 2
  m_LightLayerMask: 1
  m_RenderingLayers: 1
  m_CustomShadowLayers: 0
  m_ShadowLayerMask: 1
  m_ShadowRenderingLayers: 1
  m_LightCookieSize: {x: 1, y: 1}
  m_LightCookieOffset: {x: 0, y: 0}
  m_SoftShadowQuality: 1
--- !u!4 &427868379 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 476192706533227383, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
  m_PrefabInstance: {fileID: 6149037546204482254}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &444281374
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 444281375}
  - component: {fileID: 444281377}
  - component: {fileID: 444281376}
  m_Layer: 0
  m_Name: Icon
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &444281375
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 444281374}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 61628569}
  m_Father: {fileID: 2006396558}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: -20, y: -20}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &444281376
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 444281374}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0, g: 1, b: 0.8949728, a: 0.45490196}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &444281377
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 444281374}
  m_CullTransparentMesh: 1
--- !u!1 &457682851
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 457682852}
  - component: {fileID: 457682854}
  - component: {fileID: 457682853}
  m_Layer: 0
  m_Name: Text (TMP)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &457682852
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 457682851}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 560479876}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 1, y: 0}
  m_AnchorMax: {x: 1, y: 0}
  m_AnchoredPosition: {x: -18.6604, y: 5.2325}
  m_SizeDelta: {x: 37.3207, y: 9.9206}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &457682853
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 457682851}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: 
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_sharedMaterial: {fileID: 2180264, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4278190335
  m_fontColor: {r: 1, g: 0, b: 0, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 36
  m_fontSizeBase: 36
  m_fontWeight: 400
  m_enableAutoSizing: 1
  m_fontSizeMin: 1
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 4
  m_VerticalAlignment: 1024
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 0
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!222 &457682854
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 457682851}
  m_CullTransparentMesh: 1
--- !u!1 &463230263
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 463230266}
  - component: {fileID: 463230265}
  - component: {fileID: 463230264}
  m_Layer: 0
  m_Name: Terrain
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 2147483647
  m_IsActive: 1
--- !u!154 &463230264
TerrainCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 463230263}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_TerrainData: {fileID: 15600000, guid: 8da511a6996b8cf45b6e74a88489df2d, type: 2}
  m_EnableTreeColliders: 1
--- !u!218 &463230265
Terrain:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 463230263}
  m_Enabled: 1
  serializedVersion: 6
  m_TerrainData: {fileID: 15600000, guid: 8da511a6996b8cf45b6e74a88489df2d, type: 2}
  m_TreeDistance: 5000
  m_TreeBillboardDistance: 50
  m_TreeCrossFadeLength: 5
  m_TreeMaximumFullLODCount: 50
  m_DetailObjectDistance: 80
  m_DetailObjectDensity: 1
  m_HeightmapPixelError: 5
  m_SplatMapDistance: 1000
  m_HeightmapMinimumLODSimplification: 0
  m_HeightmapMaximumLOD: 0
  m_ShadowCastingMode: 2
  m_DrawHeightmap: 1
  m_DrawInstanced: 0
  m_DrawTreesAndFoliage: 1
  m_StaticShadowCaster: 0
  m_IgnoreQualitySettings: 0
  m_ReflectionProbeUsage: 1
  m_MaterialTemplate: {fileID: 2100000, guid: 594ea882c5a793440b60ff72d896021e, type: 2}
  m_BakeLightProbesForTrees: 1
  m_PreserveTreePrototypeLayers: 0
  m_DeringLightProbesForTrees: 1
  m_ReceiveGI: 1
  m_ScaleInLightmap: 0.0256
  m_LightmapParameters: {fileID: 15203, guid: 0000000000000000f000000000000000, type: 0}
  m_GroupingID: 0
  m_RenderingLayerMask: 1
  m_AllowAutoConnect: 1
  m_EnableHeightmapRayTracing: 1
  m_EnableTreesAndDetailsRayTracing: 0
  m_TreeMotionVectorModeOverride: 3
--- !u!4 &463230266
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 463230263}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &475421017
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 475421021}
  - component: {fileID: 475421020}
  - component: {fileID: 475421022}
  - component: {fileID: 475421019}
  - component: {fileID: 475421023}
  m_Layer: 0
  m_Name: Inventory UI
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &475421019
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 475421017}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0783879a345b80e4eb1478d837d0b7e5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Bag: {fileID: 325580531}
  targetInventory: {fileID: 858718443}
  inventoryPanel: {fileID: 0}
  inventorySlotPrefab: {fileID: 5553402093153446677, guid: 760c1ab5ae139b34d8a3f2d03de344db, type: 3}
  slotPanel: {fileID: 687848106}
  HotbarPanel: {fileID: 0}
  itemModelPresenterPrefab: {fileID: 2089220023221089146, guid: 43f109199fa2ac649893710efb217140, type: 3}
  modelPresenterParent: {fileID: 687848106}
  defaultModelOffset: {x: 0, y: 0, z: 0}
  defaultModelScaleFactor: 1
  playerCharacterTransform: {fileID: 0}
  validPlacementColor: {r: 0, g: 1, b: 0, a: 0.3}
  invalidPlacementColor: {r: 1, g: 0, b: 0, a: 0.3}
  MOUSE_ROTATE_ACTION: {fileID: 0}
  gamepadSelectedSlotHighlightColor: {r: 1, g: 0.92, b: 0.016, a: 0.5}
  GAMEPAD_NAVIGATE_ACTION: {fileID: 0}
  GAMEPAD_SELECT_ACTION: {fileID: 0}
  GAMEPAD_ROTATE_ACTION: {fileID: 0}
  GAMEPAD_CANCEL_ACTION: {fileID: 0}
--- !u!223 &475421020
Canvas:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 475421017}
  m_Enabled: 1
  serializedVersion: 3
  m_RenderMode: 2
  m_Camera: {fileID: 26822135}
  m_PlaneDistance: 100
  m_PixelPerfect: 0
  m_ReceivesEvents: 1
  m_OverrideSorting: 0
  m_OverridePixelPerfect: 0
  m_SortingBucketNormalizedSize: 0
  m_VertexColorAlwaysGammaSpace: 0
  m_AdditionalShaderChannelsFlag: 25
  m_UpdateRectTransformForStandalone: 0
  m_SortingLayerID: 0
  m_SortingOrder: 0
  m_TargetDisplay: 0
--- !u!224 &475421021
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 475421017}
  m_LocalRotation: {x: 0, y: -1, z: -0, w: -0.0000000037252903}
  m_LocalPosition: {x: 0, y: 0, z: 0.51000077}
  m_LocalScale: {x: 0.001, y: 0.001, z: 0.001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1499340464}
  - {fileID: 346950623}
  m_Father: {fileID: 2115109471}
  m_LocalEulerAnglesHint: {x: 0, y: 180, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0.0000001899898, y: 0}
  m_SizeDelta: {x: 800, y: 640}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &475421022
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 475421017}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc42784cf147c0c48a680349fa168899, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IgnoreReversedGraphics: 1
  m_BlockingObjects: 0
  m_BlockingMask:
    serializedVersion: 2
    m_Bits: 4294967295
--- !u!114 &475421023
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 475421017}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f3b9f83b3df505441a0a91f16f1ae084, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  targetObject: {fileID: 2115109467}
  targetCanvas: {fileID: 475421020}
  offsetDistance: 0.01
  padding: 0.05
--- !u!1 &481624696
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 481624697}
  - component: {fileID: 481624700}
  - component: {fileID: 481624699}
  - component: {fileID: 481624698}
  m_Layer: 0
  m_Name: Slot 6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &481624697
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 481624696}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1976096064}
  - {fileID: 2044630220}
  - {fileID: 2016954412}
  m_Father: {fileID: 687848106}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!225 &481624698
CanvasGroup:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 481624696}
  m_Enabled: 1
  m_Alpha: 1
  m_Interactable: 1
  m_BlocksRaycasts: 1
  m_IgnoreParentGroups: 0
--- !u!114 &481624699
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 481624696}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4e29b1a8efbd4b44bb3f3716e73f07ff, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.92993355, g: 1, b: 0, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 1976096065}
  m_OnClick:
    m_PersistentCalls:
      m_Calls: []
--- !u!114 &481624700
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 481624696}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b2bd544dc8bd07147b60d76c0c13a6f3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemIconImage: {fileID: 0}
  backgroundImage: {fileID: 1976096065}
  quantityText: {fileID: 658101685}
  parentUI: {fileID: 475421019}
  defaultBackgroundColor: {r: 1, g: 1, b: 1, a: 1}
  coveredBackgroundColor: {r: 0.8, g: 0.8, b: 0.8, a: 0.5}
  occupiedBackgroundColor: {r: 0.9, g: 0.9, b: 0.9, a: 1}
  slotIndexInInventory: 0
  CurrentModelPresenter: {fileID: 0}
  selectionHighlightImage: {fileID: 0}
--- !u!1 &545318120
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 545318121}
  - component: {fileID: 545318123}
  - component: {fileID: 545318122}
  m_Layer: 0
  m_Name: Image
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &545318121
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 545318120}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 110915053}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &545318122
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 545318120}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &545318123
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 545318120}
  m_CullTransparentMesh: 1
--- !u!1 &550914657
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 550914658}
  m_Layer: 0
  m_Name: Hit position
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &550914658
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 550914657}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1548139918}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &560479875
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 560479876}
  - component: {fileID: 560479878}
  - component: {fileID: 560479877}
  m_Layer: 0
  m_Name: Icon
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &560479876
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 560479875}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 457682852}
  m_Father: {fileID: 110915053}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: -20, y: -20}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &560479877
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 560479875}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0, g: 1, b: 0.8949728, a: 0.45490196}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &560479878
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 560479875}
  m_CullTransparentMesh: 1
--- !u!1 &601210454
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 601210455}
  m_Layer: 0
  m_Name: Spawn Item
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &601210455
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 601210454}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: -24}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2044205516}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1 &606755173
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 606755174}
  m_Layer: 0
  m_Name: Spawn Item
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &606755174
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 606755173}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: -24}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2006396558}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1 &645298389
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 645298390}
  - component: {fileID: 645298392}
  - component: {fileID: 645298391}
  m_Layer: 0
  m_Name: Text (TMP)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &645298390
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 645298389}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 721668125}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 1, y: 0}
  m_AnchorMax: {x: 1, y: 0}
  m_AnchoredPosition: {x: -18.6604, y: 5.2325}
  m_SizeDelta: {x: 37.3207, y: 9.9206}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &645298391
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 645298389}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: 
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_sharedMaterial: {fileID: 2180264, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4278190335
  m_fontColor: {r: 1, g: 0, b: 0, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 36
  m_fontSizeBase: 36
  m_fontWeight: 400
  m_enableAutoSizing: 1
  m_fontSizeMin: 1
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 4
  m_VerticalAlignment: 1024
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 0
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!222 &645298392
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 645298389}
  m_CullTransparentMesh: 1
--- !u!1 &652855086
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 652855087}
  - component: {fileID: 652855089}
  - component: {fileID: 652855088}
  m_Layer: 0
  m_Name: Image
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &652855087
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 652855086}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 763895328}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &652855088
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 652855086}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &652855089
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 652855086}
  m_CullTransparentMesh: 1
--- !u!1 &658101683
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 658101684}
  - component: {fileID: 658101686}
  - component: {fileID: 658101685}
  m_Layer: 0
  m_Name: Text (TMP)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &658101684
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 658101683}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2016954412}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 1, y: 0}
  m_AnchorMax: {x: 1, y: 0}
  m_AnchoredPosition: {x: -18.6604, y: 5.2325}
  m_SizeDelta: {x: 37.3207, y: 9.9206}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &658101685
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 658101683}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: 
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_sharedMaterial: {fileID: 2180264, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4278190335
  m_fontColor: {r: 1, g: 0, b: 0, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 36
  m_fontSizeBase: 36
  m_fontWeight: 400
  m_enableAutoSizing: 1
  m_fontSizeMin: 1
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 4
  m_VerticalAlignment: 1024
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 0
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!222 &658101686
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 658101683}
  m_CullTransparentMesh: 1
--- !u!1 &658427056
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: *********}
  - component: {fileID: 658427059}
  - component: {fileID: 658427058}
  m_Layer: 0
  m_Name: Text (TMP)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &*********
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 658427056}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: *********}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 1, y: 0}
  m_AnchorMax: {x: 1, y: 0}
  m_AnchoredPosition: {x: -18.6604, y: 5.2325}
  m_SizeDelta: {x: 37.3207, y: 9.9206}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &658427058
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 658427056}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: 
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_sharedMaterial: {fileID: 2180264, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4278190335
  m_fontColor: {r: 1, g: 0, b: 0, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 36
  m_fontSizeBase: 36
  m_fontWeight: 400
  m_enableAutoSizing: 1
  m_fontSizeMin: 1
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 4
  m_VerticalAlignment: 1024
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 0
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!222 &658427059
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 658427056}
  m_CullTransparentMesh: 1
--- !u!1 &664271922
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 664271923}
  - component: {fileID: 664271925}
  - component: {fileID: 664271924}
  m_Layer: 0
  m_Name: Image
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &664271923
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 664271922}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1165677728}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &664271924
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 664271922}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &664271925
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 664271922}
  m_CullTransparentMesh: 1
--- !u!1 &665718894 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 7831802660052190827, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
  m_PrefabInstance: {fileID: 6149037546204482254}
  m_PrefabAsset: {fileID: 0}
--- !u!223 &665718896
Canvas:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 665718894}
  m_Enabled: 1
  serializedVersion: 3
  m_RenderMode: 1
  m_Camera: {fileID: 1152102166}
  m_PlaneDistance: 100
  m_PixelPerfect: 0
  m_ReceivesEvents: 1
  m_OverrideSorting: 0
  m_OverridePixelPerfect: 0
  m_SortingBucketNormalizedSize: 0
  m_VertexColorAlwaysGammaSpace: 0
  m_AdditionalShaderChannelsFlag: 25
  m_UpdateRectTransformForStandalone: 0
  m_SortingLayerID: 0
  m_SortingOrder: 0
  m_TargetDisplay: 0
--- !u!224 &665718897 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 7488457649872112934, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
  m_PrefabInstance: {fileID: 6149037546204482254}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &666423299
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 666423300}
  - component: {fileID: 666423302}
  - component: {fileID: 666423301}
  m_Layer: 0
  m_Name: Text (TMP)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &666423300
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 666423299}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 781639908}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 1, y: 0}
  m_AnchorMax: {x: 1, y: 0}
  m_AnchoredPosition: {x: -18.6604, y: 5.2325}
  m_SizeDelta: {x: 37.3207, y: 9.9206}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &666423301
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 666423299}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: 
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_sharedMaterial: {fileID: 2180264, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4278190335
  m_fontColor: {r: 1, g: 0, b: 0, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 36
  m_fontSizeBase: 36
  m_fontWeight: 400
  m_enableAutoSizing: 1
  m_fontSizeMin: 1
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 4
  m_VerticalAlignment: 1024
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 0
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!222 &666423302
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 666423299}
  m_CullTransparentMesh: 1
--- !u!1 &671427667
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 671427668}
  - component: {fileID: 671427670}
  - component: {fileID: 671427669}
  m_Layer: 0
  m_Name: Image
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &671427668
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 671427667}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1484430348}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &671427669
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 671427667}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &671427670
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 671427667}
  m_CullTransparentMesh: 1
--- !u!1001 &680254468
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 3297611622053069906, guid: 019c3fa5df243b4409c001556a2cf38f, type: 3}
      propertyPath: m_LocalPosition.x
      value: -3.4519322
      objectReference: {fileID: 0}
    - target: {fileID: 3297611622053069906, guid: 019c3fa5df243b4409c001556a2cf38f, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.4055365
      objectReference: {fileID: 0}
    - target: {fileID: 3297611622053069906, guid: 019c3fa5df243b4409c001556a2cf38f, type: 3}
      propertyPath: m_LocalPosition.z
      value: 29.887205
      objectReference: {fileID: 0}
    - target: {fileID: 3297611622053069906, guid: 019c3fa5df243b4409c001556a2cf38f, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 3297611622053069906, guid: 019c3fa5df243b4409c001556a2cf38f, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3297611622053069906, guid: 019c3fa5df243b4409c001556a2cf38f, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3297611622053069906, guid: 019c3fa5df243b4409c001556a2cf38f, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3297611622053069906, guid: 019c3fa5df243b4409c001556a2cf38f, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3297611622053069906, guid: 019c3fa5df243b4409c001556a2cf38f, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3297611622053069906, guid: 019c3fa5df243b4409c001556a2cf38f, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4806514915823510784, guid: 019c3fa5df243b4409c001556a2cf38f, type: 3}
      propertyPath: inputActions
      value: 
      objectReference: {fileID: -944628639613478452, guid: 5ad5980bcdd09b14a94464ddb5fb637d, type: 3}
    - target: {fileID: 6828404996325416630, guid: 019c3fa5df243b4409c001556a2cf38f, type: 3}
      propertyPath: m_Name
      value: GameManager
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 019c3fa5df243b4409c001556a2cf38f, type: 3}
--- !u!1 &*********
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 687848106}
  - component: {fileID: *********}
  m_Layer: 0
  m_Name: Content
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &687848106
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: *********}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 782819668}
  - {fileID: 2006396558}
  - {fileID: 1660565378}
  - {fileID: 1532259060}
  - {fileID: 763895328}
  - {fileID: 971408196}
  - {fileID: 481624697}
  - {fileID: 1484430348}
  - {fileID: 161268398}
  - {fileID: 110915053}
  - {fileID: *********}
  - {fileID: 2044205516}
  - {fileID: 391214796}
  - {fileID: 1587012605}
  - {fileID: 1912301149}
  - {fileID: 1668313608}
  - {fileID: 1165677728}
  - {fileID: 1172402183}
  - {fileID: 261096444}
  - {fileID: 1445181762}
  m_Father: {fileID: 1936227667}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 1}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: -0.000091552734, y: -0.0000064337705}
  m_SizeDelta: {x: 0, y: 300}
  m_Pivot: {x: 0, y: 1}
--- !u!114 &*********
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: *********}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8a8695521f0d02e499659fee002a26c2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Padding:
    m_Left: 0
    m_Right: 0
    m_Top: 0
    m_Bottom: 0
  m_ChildAlignment: 0
  m_StartCorner: 0
  m_StartAxis: 0
  m_CellSize: {x: 200, y: 200}
  m_Spacing: {x: 0, y: 0}
  m_Constraint: 0
  m_ConstraintCount: 2
--- !u!1 &*********
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: *********}
  - component: {fileID: *********}
  - component: {fileID: *********}
  m_Layer: 0
  m_Name: Icon
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &*********
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: *********}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: *********}
  m_Father: {fileID: *********}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: -20, y: -20}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &*********
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: *********}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0, g: 1, b: 0.8949728, a: 0.45490196}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &*********
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: *********}
  m_CullTransparentMesh: 1
--- !u!1 &692861815
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 692861816}
  - component: {fileID: 692861818}
  - component: {fileID: 692861817}
  m_Layer: 0
  m_Name: Icon
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &692861816
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 692861815}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1192626846}
  m_Father: {fileID: 1532259060}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: -20, y: -20}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &692861817
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 692861815}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0, g: 1, b: 0.8949728, a: 0.45490196}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &692861818
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 692861815}
  m_CullTransparentMesh: 1
--- !u!1 &714248099
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 714248100}
  - component: {fileID: 714248102}
  - component: {fileID: 714248101}
  m_Layer: 0
  m_Name: Image
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &714248100
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 714248099}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1660565378}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &714248101
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 714248099}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &714248102
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 714248099}
  m_CullTransparentMesh: 1
--- !u!1 &721668124
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 721668125}
  - component: {fileID: 721668127}
  - component: {fileID: 721668126}
  m_Layer: 0
  m_Name: Icon
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &721668125
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 721668124}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 645298390}
  m_Father: {fileID: 971408196}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: -20, y: -20}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &721668126
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 721668124}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0, g: 1, b: 0.8949728, a: 0.45490196}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &721668127
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 721668124}
  m_CullTransparentMesh: 1
--- !u!1 &731424133
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 731424134}
  m_Layer: 0
  m_Name: Spawn Item
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &731424134
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 731424133}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: -24}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 763895328}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1 &733408585
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 733408586}
  - component: {fileID: 733408588}
  - component: {fileID: 733408587}
  m_Layer: 0
  m_Name: Icon
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &733408586
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 733408585}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1717622683}
  m_Father: {fileID: 161268398}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: -20, y: -20}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &733408587
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 733408585}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0, g: 1, b: 0.8949728, a: 0.45490196}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &733408588
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 733408585}
  m_CullTransparentMesh: 1
--- !u!1 &742080239
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 742080240}
  m_Layer: 0
  m_Name: Spawn Item
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &742080240
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 742080239}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: -24}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 782819668}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1 &751036126
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 751036127}
  - component: {fileID: 751036129}
  - component: {fileID: 751036128}
  m_Layer: 0
  m_Name: Image
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &751036127
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 751036126}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 971408196}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &751036128
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 751036126}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &751036129
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 751036126}
  m_CullTransparentMesh: 1
--- !u!1 &753000208
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 753000209}
  - component: {fileID: 753000211}
  - component: {fileID: 753000210}
  m_Layer: 0
  m_Name: Image
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &753000209
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 753000208}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1532259060}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &753000210
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 753000208}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &753000211
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 753000208}
  m_CullTransparentMesh: 1
--- !u!1 &763895327
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 763895328}
  - component: {fileID: 763895331}
  - component: {fileID: 763895330}
  - component: {fileID: 763895329}
  m_Layer: 0
  m_Name: Slot 4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &763895328
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 763895327}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 652855087}
  - {fileID: 731424134}
  - {fileID: 408831303}
  m_Father: {fileID: 687848106}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!225 &763895329
CanvasGroup:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 763895327}
  m_Enabled: 1
  m_Alpha: 1
  m_Interactable: 1
  m_BlocksRaycasts: 1
  m_IgnoreParentGroups: 0
--- !u!114 &763895330
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 763895327}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4e29b1a8efbd4b44bb3f3716e73f07ff, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.92993355, g: 1, b: 0, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 652855088}
  m_OnClick:
    m_PersistentCalls:
      m_Calls: []
--- !u!114 &763895331
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 763895327}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b2bd544dc8bd07147b60d76c0c13a6f3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemIconImage: {fileID: 0}
  backgroundImage: {fileID: 652855088}
  quantityText: {fileID: 210829133}
  parentUI: {fileID: 475421019}
  defaultBackgroundColor: {r: 1, g: 1, b: 1, a: 1}
  coveredBackgroundColor: {r: 0.8, g: 0.8, b: 0.8, a: 0.5}
  occupiedBackgroundColor: {r: 0.9, g: 0.9, b: 0.9, a: 1}
  slotIndexInInventory: 0
  CurrentModelPresenter: {fileID: 0}
  selectionHighlightImage: {fileID: 0}
--- !u!1 &779154121
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 779154122}
  - component: {fileID: 779154124}
  - component: {fileID: 779154123}
  m_Layer: 0
  m_Name: Icon
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &779154122
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 779154121}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1864769041}
  m_Father: {fileID: 1445181762}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: -20, y: -20}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &779154123
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 779154121}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0, g: 1, b: 0.8949728, a: 0.45490196}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &779154124
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 779154121}
  m_CullTransparentMesh: 1
--- !u!1 &780809929
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 780809930}
  - component: {fileID: 780809932}
  - component: {fileID: 780809931}
  m_Layer: 0
  m_Name: Icon
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &780809930
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 780809929}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 358398579}
  m_Father: {fileID: 1484430348}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: -20, y: -20}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &780809931
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 780809929}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0, g: 1, b: 0.8949728, a: 0.45490196}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &780809932
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 780809929}
  m_CullTransparentMesh: 1
--- !u!1 &781639907
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 781639908}
  - component: {fileID: 781639910}
  - component: {fileID: 781639909}
  m_Layer: 0
  m_Name: Icon
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &781639908
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 781639907}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 666423300}
  m_Father: {fileID: 391214796}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: -20, y: -20}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &781639909
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 781639907}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0, g: 1, b: 0.8949728, a: 0.45490196}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &781639910
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 781639907}
  m_CullTransparentMesh: 1
--- !u!1 &782819667
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 782819668}
  - component: {fileID: 782819671}
  - component: {fileID: 782819670}
  - component: {fileID: 782819669}
  m_Layer: 0
  m_Name: Slot 0
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &782819668
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 782819667}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1645645476}
  - {fileID: 742080240}
  - {fileID: 1118347057}
  m_Father: {fileID: 687848106}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!225 &782819669
CanvasGroup:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 782819667}
  m_Enabled: 1
  m_Alpha: 1
  m_Interactable: 1
  m_BlocksRaycasts: 1
  m_IgnoreParentGroups: 0
--- !u!114 &782819670
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 782819667}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4e29b1a8efbd4b44bb3f3716e73f07ff, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.92993355, g: 1, b: 0, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 1645645477}
  m_OnClick:
    m_PersistentCalls:
      m_Calls: []
--- !u!114 &782819671
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 782819667}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b2bd544dc8bd07147b60d76c0c13a6f3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemIconImage: {fileID: 0}
  backgroundImage: {fileID: 1645645477}
  quantityText: {fileID: 876546469}
  parentUI: {fileID: 475421019}
  defaultBackgroundColor: {r: 1, g: 1, b: 1, a: 1}
  coveredBackgroundColor: {r: 0.8, g: 0.8, b: 0.8, a: 0.5}
  occupiedBackgroundColor: {r: 0.9, g: 0.9, b: 0.9, a: 1}
  slotIndexInInventory: 0
  CurrentModelPresenter: {fileID: 0}
  selectionHighlightImage: {fileID: 0}
--- !u!1 &832575517
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 832575519}
  - component: {fileID: 832575518}
  m_Layer: 0
  m_Name: Global Volume
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &832575518
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 832575517}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 172515602e62fb746b5d573b38a5fe58, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IsGlobal: 1
  priority: 0
  blendDistance: 0
  weight: 1
  sharedProfile: {fileID: 11400000, guid: 10fc4df2da32a41aaa32d77bc913491c, type: 2}
--- !u!4 &832575519
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 832575517}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &858718427 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 2786292861261399877, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
  m_PrefabInstance: {fileID: 6149037546204482254}
  m_PrefabAsset: {fileID: 0}
--- !u!4 &858718430 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 3474384654803765789, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
  m_PrefabInstance: {fileID: 6149037546204482254}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &858718432 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 4705984108871589067, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
  m_PrefabInstance: {fileID: 6149037546204482254}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 858718427}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1430d86b451d4db438a67a9dc15ac7b0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &858718435 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
  m_PrefabInstance: {fileID: 6149037546204482254}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 858718427}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0c6c06b39d112bc4982bcfc6a2e28923, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &858718439
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 858718427}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6f3d2135272fc5f4aa987100965ebb0f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  availableEffects: []
  activeEffects: []
  playerMono: {fileID: 858718432}
  references:
    version: 2
    RefIds: []
--- !u!114 &858718443
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 858718427}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 934dce1e944f14943a6d9a125f199b58, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  slots: []
  capacity: 20
  rows: 4
  columns: 4
  inventoryUI: {fileID: 2109280228}
  MyInput: {fileID: 1875963270}
  InventoryBinding: {fileID: 0}
--- !u!1 &876546467
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 876546468}
  - component: {fileID: 876546470}
  - component: {fileID: 876546469}
  m_Layer: 0
  m_Name: Text (TMP)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &876546468
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 876546467}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1118347057}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 1, y: 0}
  m_AnchorMax: {x: 1, y: 0}
  m_AnchoredPosition: {x: -18.6604, y: 5.2325}
  m_SizeDelta: {x: 37.3207, y: 9.9206}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &876546469
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 876546467}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: 
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_sharedMaterial: {fileID: 2180264, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4278190335
  m_fontColor: {r: 1, g: 0, b: 0, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 36
  m_fontSizeBase: 36
  m_fontWeight: 400
  m_enableAutoSizing: 1
  m_fontSizeMin: 1
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 4
  m_VerticalAlignment: 1024
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 0
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!222 &876546470
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 876546467}
  m_CullTransparentMesh: 1
--- !u!1 &906778884
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 906778885}
  - component: {fileID: 906778889}
  - component: {fileID: 906778888}
  - component: {fileID: 906778887}
  - component: {fileID: 906778886}
  m_Layer: 0
  m_Name: Sphere
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &906778885
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 906778884}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: -4.05}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1216155667}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &906778886
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 906778884}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f2811051fda2a02499b5348c3c1f6c10, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  interactHint:
    crosshairIconOverride: {fileID: 0}
    crosshairTextOverride: 
    HasCondition: 0
    conditionSO: []
  lookingAtEvents: []
  fieldOfViewEvents: []
--- !u!135 &906778887
SphereCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 906778884}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Radius: 0.5
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &906778888
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 906778884}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 31321ba15b8f8eb4c954353edc038b1d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &906778889
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 906778884}
  m_Mesh: {fileID: 10207, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &910774342 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 8103734383454721524, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
  m_PrefabInstance: {fileID: 6149037546204482254}
  m_PrefabAsset: {fileID: 0}
--- !u!224 &910774343 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 1169318483775943096, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
  m_PrefabInstance: {fileID: 6149037546204482254}
  m_PrefabAsset: {fileID: 0}
--- !u!222 &910774345
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 910774342}
  m_CullTransparentMesh: 1
--- !u!1 &966297718
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 966297719}
  m_Layer: 0
  m_Name: Spawn Item
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &966297719
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 966297718}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: -24}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1445181762}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1 &971408195
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 971408196}
  - component: {fileID: 971408199}
  - component: {fileID: 971408198}
  - component: {fileID: 971408197}
  m_Layer: 0
  m_Name: Slot 5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &971408196
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 971408195}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 751036127}
  - {fileID: 407994735}
  - {fileID: 721668125}
  m_Father: {fileID: 687848106}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!225 &971408197
CanvasGroup:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 971408195}
  m_Enabled: 1
  m_Alpha: 1
  m_Interactable: 1
  m_BlocksRaycasts: 1
  m_IgnoreParentGroups: 0
--- !u!114 &971408198
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 971408195}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4e29b1a8efbd4b44bb3f3716e73f07ff, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.92993355, g: 1, b: 0, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 751036128}
  m_OnClick:
    m_PersistentCalls:
      m_Calls: []
--- !u!114 &971408199
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 971408195}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b2bd544dc8bd07147b60d76c0c13a6f3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemIconImage: {fileID: 0}
  backgroundImage: {fileID: 751036128}
  quantityText: {fileID: 645298391}
  parentUI: {fileID: 475421019}
  defaultBackgroundColor: {r: 1, g: 1, b: 1, a: 1}
  coveredBackgroundColor: {r: 0.8, g: 0.8, b: 0.8, a: 0.5}
  occupiedBackgroundColor: {r: 0.9, g: 0.9, b: 0.9, a: 1}
  slotIndexInInventory: 0
  CurrentModelPresenter: {fileID: 0}
  selectionHighlightImage: {fileID: 0}
--- !u!1 &1015810711
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1015810712}
  - component: {fileID: 1015810714}
  - component: {fileID: 1015810713}
  - component: {fileID: 1015810715}
  m_Layer: 5
  m_Name: Panel
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1015810712
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1015810711}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1585922017}
  m_Father: {fileID: 6576953}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1015810713
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1015810711}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 0.392}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 10907, guid: 0000000000000000f000000000000000, type: 0}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1015810714
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1015810711}
  m_CullTransparentMesh: 1
--- !u!23 &1015810715
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1015810711}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10100, guid: 0000000000000000e000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &1034575648 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 8038906410268111794, guid: c5567364c5ebcf441b0106b7017b2d9c, type: 3}
  m_PrefabInstance: {fileID: 1666664377295561245}
  m_PrefabAsset: {fileID: 0}
--- !u!54 &1034575649
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1034575648}
  serializedVersion: 4
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!1 &1042441659
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1042441660}
  - component: {fileID: 1042441662}
  - component: {fileID: 1042441661}
  m_Layer: 5
  m_Name: Panel
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1042441660
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1042441659}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 316418724}
  m_Father: {fileID: 1962102720}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1042441661
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1042441659}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 0.392}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 10907, guid: 0000000000000000f000000000000000, type: 0}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1042441662
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1042441659}
  m_CullTransparentMesh: 1
--- !u!1 &1060004809
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1060004810}
  - component: {fileID: 1060004812}
  - component: {fileID: 1060004811}
  m_Layer: 0
  m_Name: Image
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1060004810
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1060004809}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2044205516}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1060004811
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1060004809}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1060004812
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1060004809}
  m_CullTransparentMesh: 1
--- !u!1 &1098140103
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1098140104}
  m_Layer: 0
  m_Name: Spawn Item
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1098140104
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1098140103}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: -24}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1532259060}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1 &1118347056
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1118347057}
  - component: {fileID: 1118347059}
  - component: {fileID: 1118347058}
  m_Layer: 0
  m_Name: Icon
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1118347057
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1118347056}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 876546468}
  m_Father: {fileID: 782819668}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: -20, y: -20}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1118347058
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1118347056}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0, g: 1, b: 0.8949728, a: 0.45490196}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1118347059
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1118347056}
  m_CullTransparentMesh: 1
--- !u!20 &1152102166 stripped
Camera:
  m_CorrespondingSourceObject: {fileID: 6104574021564369951, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
  m_PrefabInstance: {fileID: 6149037546204482254}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1165677727
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1165677728}
  - component: {fileID: 1165677731}
  - component: {fileID: 1165677730}
  - component: {fileID: 1165677729}
  m_Layer: 0
  m_Name: Slot 16
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1165677728
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1165677727}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 664271923}
  - {fileID: 1773715524}
  - {fileID: 1899777960}
  m_Father: {fileID: 687848106}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!225 &1165677729
CanvasGroup:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1165677727}
  m_Enabled: 1
  m_Alpha: 1
  m_Interactable: 1
  m_BlocksRaycasts: 1
  m_IgnoreParentGroups: 0
--- !u!114 &1165677730
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1165677727}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4e29b1a8efbd4b44bb3f3716e73f07ff, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.92993355, g: 1, b: 0, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 664271924}
  m_OnClick:
    m_PersistentCalls:
      m_Calls: []
--- !u!114 &1165677731
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1165677727}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b2bd544dc8bd07147b60d76c0c13a6f3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemIconImage: {fileID: 0}
  backgroundImage: {fileID: 664271924}
  quantityText: {fileID: 1678824430}
  parentUI: {fileID: 475421019}
  defaultBackgroundColor: {r: 1, g: 1, b: 1, a: 1}
  coveredBackgroundColor: {r: 0.8, g: 0.8, b: 0.8, a: 0.5}
  occupiedBackgroundColor: {r: 0.9, g: 0.9, b: 0.9, a: 1}
  slotIndexInInventory: 0
  CurrentModelPresenter: {fileID: 0}
  selectionHighlightImage: {fileID: 0}
--- !u!1 &1169943726
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1169943727}
  - component: {fileID: 1169943729}
  - component: {fileID: 1169943728}
  m_Layer: 0
  m_Name: Handle
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1169943727
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1169943726}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1960457737}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 20, y: 20}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1169943728
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1169943726}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 10905, guid: 0000000000000000f000000000000000, type: 0}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1169943729
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1169943726}
  m_CullTransparentMesh: 1
--- !u!1 &1172402182
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1172402183}
  - component: {fileID: 1172402186}
  - component: {fileID: 1172402185}
  - component: {fileID: 1172402184}
  m_Layer: 0
  m_Name: Slot 17
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1172402183
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1172402182}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 192052687}
  - {fileID: 82983281}
  - {fileID: 1305318318}
  m_Father: {fileID: 687848106}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!225 &1172402184
CanvasGroup:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1172402182}
  m_Enabled: 1
  m_Alpha: 1
  m_Interactable: 1
  m_BlocksRaycasts: 1
  m_IgnoreParentGroups: 0
--- !u!114 &1172402185
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1172402182}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4e29b1a8efbd4b44bb3f3716e73f07ff, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.92993355, g: 1, b: 0, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 192052688}
  m_OnClick:
    m_PersistentCalls:
      m_Calls: []
--- !u!114 &1172402186
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1172402182}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b2bd544dc8bd07147b60d76c0c13a6f3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemIconImage: {fileID: 0}
  backgroundImage: {fileID: 192052688}
  quantityText: {fileID: 1569880553}
  parentUI: {fileID: 475421019}
  defaultBackgroundColor: {r: 1, g: 1, b: 1, a: 1}
  coveredBackgroundColor: {r: 0.8, g: 0.8, b: 0.8, a: 0.5}
  occupiedBackgroundColor: {r: 0.9, g: 0.9, b: 0.9, a: 1}
  slotIndexInInventory: 0
  CurrentModelPresenter: {fileID: 0}
  selectionHighlightImage: {fileID: 0}
--- !u!1 &1192626845
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1192626846}
  - component: {fileID: 1192626848}
  - component: {fileID: 1192626847}
  m_Layer: 0
  m_Name: Text (TMP)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1192626846
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1192626845}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 692861816}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 1, y: 0}
  m_AnchorMax: {x: 1, y: 0}
  m_AnchoredPosition: {x: -18.6604, y: 5.2325}
  m_SizeDelta: {x: 37.3207, y: 9.9206}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1192626847
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1192626845}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: 
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_sharedMaterial: {fileID: 2180264, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4278190335
  m_fontColor: {r: 1, g: 0, b: 0, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 36
  m_fontSizeBase: 36
  m_fontWeight: 400
  m_enableAutoSizing: 1
  m_fontSizeMin: 1
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 4
  m_VerticalAlignment: 1024
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 0
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!222 &1192626848
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1192626845}
  m_CullTransparentMesh: 1
--- !u!1 &1197672376
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1197672377}
  m_Layer: 0
  m_Name: Spawn Item
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1197672377
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1197672376}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: -24}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1484430348}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1 &1216155666
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1216155667}
  m_Layer: 0
  m_Name: Itens coletaveis
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1216155667
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1216155666}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 25.852652, y: 1.83, z: 25.993664}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 906778885}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1233695912
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1233695913}
  m_Layer: 0
  m_Name: Objetos escalaveis
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1233695913
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1233695912}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -2.5877066, y: -5.6744413, z: 59.040703}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1651308565}
  - {fileID: 1606239611}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1281470155
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1281470156}
  - component: {fileID: 1281470159}
  - component: {fileID: 1281470158}
  - component: {fileID: 1281470157}
  m_Layer: 0
  m_Name: Scrollbar Horizontal
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1281470156
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1281470155}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1773055873}
  m_Father: {fileID: 1471678706}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 20}
  m_Pivot: {x: 0, y: 0}
--- !u!114 &1281470157
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1281470155}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2a4db7a114972834c8e4117be1d82ba3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 1944773585}
  m_HandleRect: {fileID: 1944773584}
  m_Direction: 0
  m_Value: 0
  m_Size: 0.9999997
  m_NumberOfSteps: 0
  m_OnValueChanged:
    m_PersistentCalls:
      m_Calls: []
--- !u!114 &1281470158
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1281470155}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 10907, guid: 0000000000000000f000000000000000, type: 0}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1281470159
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1281470155}
  m_CullTransparentMesh: 1
--- !u!1 &1305318317
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1305318318}
  - component: {fileID: 1305318320}
  - component: {fileID: 1305318319}
  m_Layer: 0
  m_Name: Icon
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1305318318
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1305318317}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1569880552}
  m_Father: {fileID: 1172402183}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: -20, y: -20}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1305318319
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1305318317}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0, g: 1, b: 0.8949728, a: 0.45490196}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1305318320
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1305318317}
  m_CullTransparentMesh: 1
--- !u!1 &1339507343 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 8229108557128630271, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
  m_PrefabInstance: {fileID: 6149037546204482254}
  m_PrefabAsset: {fileID: 0}
--- !u!222 &1339507345
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1339507343}
  m_CullTransparentMesh: 1
--- !u!1 &1382638831
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1382638832}
  m_Layer: 0
  m_Name: Spawn Item
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1382638832
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1382638831}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: -24}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1660565378}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1390601010 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 339850909857526022, guid: 4fceb36ebeeaae24e9dc6a79ee9c7607, type: 3}
  m_PrefabInstance: {fileID: 4455869690388524289}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b16c6f78230fa4964a222622d8aae332, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &1390601011 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 5466767361345234659, guid: 4fceb36ebeeaae24e9dc6a79ee9c7607, type: 3}
  m_PrefabInstance: {fileID: 4455869690388524289}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 01614664b831546d2ae94a42149d80ac, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &1445181761
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1445181762}
  - component: {fileID: 1445181765}
  - component: {fileID: 1445181764}
  - component: {fileID: 1445181763}
  m_Layer: 0
  m_Name: Slot 19
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1445181762
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1445181761}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 162193659}
  - {fileID: 966297719}
  - {fileID: 779154122}
  m_Father: {fileID: 687848106}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!225 &1445181763
CanvasGroup:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1445181761}
  m_Enabled: 1
  m_Alpha: 1
  m_Interactable: 1
  m_BlocksRaycasts: 1
  m_IgnoreParentGroups: 0
--- !u!114 &1445181764
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1445181761}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4e29b1a8efbd4b44bb3f3716e73f07ff, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.92993355, g: 1, b: 0, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 162193660}
  m_OnClick:
    m_PersistentCalls:
      m_Calls: []
--- !u!114 &1445181765
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1445181761}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b2bd544dc8bd07147b60d76c0c13a6f3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemIconImage: {fileID: 0}
  backgroundImage: {fileID: 162193660}
  quantityText: {fileID: 1864769042}
  parentUI: {fileID: 475421019}
  defaultBackgroundColor: {r: 1, g: 1, b: 1, a: 1}
  coveredBackgroundColor: {r: 0.8, g: 0.8, b: 0.8, a: 0.5}
  occupiedBackgroundColor: {r: 0.9, g: 0.9, b: 0.9, a: 1}
  slotIndexInInventory: 0
  CurrentModelPresenter: {fileID: 0}
  selectionHighlightImage: {fileID: 0}
--- !u!1 &1445526240
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1445526241}
  - component: {fileID: 1445526243}
  - component: {fileID: 1445526242}
  m_Layer: 0
  m_Name: Text (TMP)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1445526241
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1445526240}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2042462911}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 1, y: 0}
  m_AnchorMax: {x: 1, y: 0}
  m_AnchoredPosition: {x: -18.6604, y: 5.2325}
  m_SizeDelta: {x: 37.3207, y: 9.9206}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1445526242
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1445526240}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: 
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_sharedMaterial: {fileID: 2180264, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4278190335
  m_fontColor: {r: 1, g: 0, b: 0, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 36
  m_fontSizeBase: 36
  m_fontWeight: 400
  m_enableAutoSizing: 1
  m_fontSizeMin: 1
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 4
  m_VerticalAlignment: 1024
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 0
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!222 &1445526243
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1445526240}
  m_CullTransparentMesh: 1
--- !u!1 &1471678705
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1471678706}
  - component: {fileID: 1471678709}
  - component: {fileID: 1471678708}
  - component: {fileID: 1471678707}
  m_Layer: 0
  m_Name: Scroll View
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1471678706
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1471678705}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1936227667}
  - {fileID: 1281470156}
  - {fileID: 1525167452}
  m_Father: {fileID: 1499340464}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: -0.000015258789}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1471678707
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1471678705}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1aa08ab6e0800fa44ae55d278d1423e3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Content: {fileID: 687848106}
  m_Horizontal: 1
  m_Vertical: 1
  m_MovementType: 1
  m_Elasticity: 0.1
  m_Inertia: 1
  m_DecelerationRate: 0.135
  m_ScrollSensitivity: 1
  m_Viewport: {fileID: 1936227667}
  m_HorizontalScrollbar: {fileID: 1281470157}
  m_VerticalScrollbar: {fileID: 1525167453}
  m_HorizontalScrollbarVisibility: 2
  m_VerticalScrollbarVisibility: 2
  m_HorizontalScrollbarSpacing: -3
  m_VerticalScrollbarSpacing: -3
  m_OnValueChanged:
    m_PersistentCalls:
      m_Calls: []
--- !u!114 &1471678708
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1471678705}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 0.392}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 10907, guid: 0000000000000000f000000000000000, type: 0}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1471678709
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1471678705}
  m_CullTransparentMesh: 1
--- !u!1 &1484430347
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1484430348}
  - component: {fileID: 1484430351}
  - component: {fileID: 1484430350}
  - component: {fileID: 1484430349}
  m_Layer: 0
  m_Name: Slot 7
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1484430348
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1484430347}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 671427668}
  - {fileID: 1197672377}
  - {fileID: 780809930}
  m_Father: {fileID: 687848106}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!225 &1484430349
CanvasGroup:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1484430347}
  m_Enabled: 1
  m_Alpha: 1
  m_Interactable: 1
  m_BlocksRaycasts: 1
  m_IgnoreParentGroups: 0
--- !u!114 &1484430350
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1484430347}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4e29b1a8efbd4b44bb3f3716e73f07ff, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.92993355, g: 1, b: 0, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 671427669}
  m_OnClick:
    m_PersistentCalls:
      m_Calls: []
--- !u!114 &1484430351
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1484430347}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b2bd544dc8bd07147b60d76c0c13a6f3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemIconImage: {fileID: 0}
  backgroundImage: {fileID: 671427669}
  quantityText: {fileID: 358398580}
  parentUI: {fileID: 475421019}
  defaultBackgroundColor: {r: 1, g: 1, b: 1, a: 1}
  coveredBackgroundColor: {r: 0.8, g: 0.8, b: 0.8, a: 0.5}
  occupiedBackgroundColor: {r: 0.9, g: 0.9, b: 0.9, a: 1}
  slotIndexInInventory: 0
  CurrentModelPresenter: {fileID: 0}
  selectionHighlightImage: {fileID: 0}
--- !u!1 &1486144096 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 5039467200435948787, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
  m_PrefabInstance: {fileID: 3504528070976578228}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &1486144098
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1486144096}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8a8695521f0d02e499659fee002a26c2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Padding:
    m_Left: 0
    m_Right: 0
    m_Top: 0
    m_Bottom: 0
  m_ChildAlignment: 0
  m_StartCorner: 0
  m_StartAxis: 0
  m_CellSize: {x: 200, y: 200}
  m_Spacing: {x: 0, y: 0}
  m_Constraint: 0
  m_ConstraintCount: 2
--- !u!224 &1486144099 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 8504204043688463548, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
  m_PrefabInstance: {fileID: 3504528070976578228}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1492303154
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1492303155}
  m_Layer: 0
  m_Name: Spawn Item
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1492303155
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1492303154}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: -24}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 110915053}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1 &1499340463
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1499340464}
  - component: {fileID: 1499340466}
  - component: {fileID: 1499340465}
  m_Layer: 0
  m_Name: Inventory BG
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1499340464
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1499340463}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1471678706}
  m_Father: {fileID: 475421021}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1499340465
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1499340463}
  m_Enabled: 0
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0, g: 0, b: 0, a: 0.46666667}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 10907, guid: 0000000000000000f000000000000000, type: 0}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1499340466
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1499340463}
  m_CullTransparentMesh: 1
--- !u!1 &1525167451
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1525167452}
  - component: {fileID: 1525167455}
  - component: {fileID: 1525167454}
  - component: {fileID: 1525167453}
  m_Layer: 0
  m_Name: Scrollbar Vertical
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1525167452
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1525167451}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1960457737}
  m_Father: {fileID: 1471678706}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 1, y: 0}
  m_AnchorMax: {x: 1, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 20, y: 0}
  m_Pivot: {x: 1, y: 1}
--- !u!114 &1525167453
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1525167451}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2a4db7a114972834c8e4117be1d82ba3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 1169943728}
  m_HandleRect: {fileID: 1169943727}
  m_Direction: 2
  m_Value: 0
  m_Size: 1
  m_NumberOfSteps: 0
  m_OnValueChanged:
    m_PersistentCalls:
      m_Calls: []
--- !u!114 &1525167454
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1525167451}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 10907, guid: 0000000000000000f000000000000000, type: 0}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1525167455
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1525167451}
  m_CullTransparentMesh: 1
--- !u!1 &1532259059
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1532259060}
  - component: {fileID: 1532259063}
  - component: {fileID: 1532259062}
  - component: {fileID: 1532259061}
  m_Layer: 0
  m_Name: Slot 3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1532259060
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1532259059}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 753000209}
  - {fileID: 1098140104}
  - {fileID: 692861816}
  m_Father: {fileID: 687848106}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!225 &1532259061
CanvasGroup:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1532259059}
  m_Enabled: 1
  m_Alpha: 1
  m_Interactable: 1
  m_BlocksRaycasts: 1
  m_IgnoreParentGroups: 0
--- !u!114 &1532259062
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1532259059}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4e29b1a8efbd4b44bb3f3716e73f07ff, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.92993355, g: 1, b: 0, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 753000210}
  m_OnClick:
    m_PersistentCalls:
      m_Calls: []
--- !u!114 &1532259063
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1532259059}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b2bd544dc8bd07147b60d76c0c13a6f3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemIconImage: {fileID: 0}
  backgroundImage: {fileID: 753000210}
  quantityText: {fileID: 1192626847}
  parentUI: {fileID: 475421019}
  defaultBackgroundColor: {r: 1, g: 1, b: 1, a: 1}
  coveredBackgroundColor: {r: 0.8, g: 0.8, b: 0.8, a: 0.5}
  occupiedBackgroundColor: {r: 0.9, g: 0.9, b: 0.9, a: 1}
  slotIndexInInventory: 0
  CurrentModelPresenter: {fileID: 0}
  selectionHighlightImage: {fileID: 0}
--- !u!1 &1547280086
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1547280087}
  m_Layer: 0
  m_Name: Spawn Item
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1547280087
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1547280086}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: -24}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 261096444}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!4 &1548139918 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 578454909220119397, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
  m_PrefabInstance: {fileID: 6149037546204482254}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1556391160
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1556391161}
  - component: {fileID: 1556391163}
  - component: {fileID: 1556391162}
  m_Layer: 0
  m_Name: Image
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1556391161
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1556391160}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: *********}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1556391162
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1556391160}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1556391163
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1556391160}
  m_CullTransparentMesh: 1
--- !u!1 &1569880551
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1569880552}
  - component: {fileID: 1569880554}
  - component: {fileID: 1569880553}
  m_Layer: 0
  m_Name: Text (TMP)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1569880552
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1569880551}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1305318318}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 1, y: 0}
  m_AnchorMax: {x: 1, y: 0}
  m_AnchoredPosition: {x: -18.6604, y: 5.2325}
  m_SizeDelta: {x: 37.3207, y: 9.9206}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1569880553
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1569880551}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: 
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_sharedMaterial: {fileID: 2180264, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4278190335
  m_fontColor: {r: 1, g: 0, b: 0, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 36
  m_fontSizeBase: 36
  m_fontWeight: 400
  m_enableAutoSizing: 1
  m_fontSizeMin: 1
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 4
  m_VerticalAlignment: 1024
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 0
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!222 &1569880554
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1569880551}
  m_CullTransparentMesh: 1
--- !u!1 &1583670867
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1583670868}
  - component: {fileID: 1583670870}
  - component: {fileID: 1583670869}
  m_Layer: 0
  m_Name: Icon
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1583670868
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1583670867}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 293746992}
  m_Father: {fileID: 1587012605}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: -20, y: -20}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1583670869
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1583670867}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0, g: 1, b: 0.8949728, a: 0.45490196}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1583670870
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1583670867}
  m_CullTransparentMesh: 1
--- !u!1 &1585922016
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1585922017}
  - component: {fileID: 1585922020}
  - component: {fileID: 1585922019}
  - component: {fileID: 1585922018}
  m_Layer: 5
  m_Name: Text (TMP)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1585922017
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1585922016}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1015810712}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 200, y: 50}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1585922018
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1585922016}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 325978a97e0443f4f926990530952b7e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  tmpTextComponent: {fileID: 1585922019}
  mySpriteAsset: {fileID: 11400000, guid: ebfdd85a058f36647bbc7e1b94ecc943, type: 2}
  spriteIndex1: -1
  spriteName1: e
  spriteIndex2: -1
  spriteName2: r
--- !u!114 &1585922019
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1585922016}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: "<color=#FFA500>Voc\xEA tem <b>100</b> <sprite name=\"e\"></color> e <color=#FF0000><b>5</b>
    <sprite name=\"r\"></color>! \n<size=75%>Isso \xE9 um texto de exemplo com <sprite
    index=0> </size>"
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_sharedMaterial: {fileID: 2180264, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4294967295
  m_fontColor: {r: 1, g: 1, b: 1, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 11400000, guid: ebfdd85a058f36647bbc7e1b94ecc943, type: 2}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 36
  m_fontSizeBase: 36
  m_fontWeight: 400
  m_enableAutoSizing: 0
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 1
  m_VerticalAlignment: 256
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 0
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!222 &1585922020
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1585922016}
  m_CullTransparentMesh: 1
--- !u!1 &1587012604
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1587012605}
  - component: {fileID: 1587012608}
  - component: {fileID: 1587012607}
  - component: {fileID: 1587012606}
  m_Layer: 0
  m_Name: Slot 13
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1587012605
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1587012604}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1591538250}
  - {fileID: 1950327573}
  - {fileID: 1583670868}
  m_Father: {fileID: 687848106}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!225 &1587012606
CanvasGroup:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1587012604}
  m_Enabled: 1
  m_Alpha: 1
  m_Interactable: 1
  m_BlocksRaycasts: 1
  m_IgnoreParentGroups: 0
--- !u!114 &1587012607
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1587012604}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4e29b1a8efbd4b44bb3f3716e73f07ff, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.92993355, g: 1, b: 0, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 1591538251}
  m_OnClick:
    m_PersistentCalls:
      m_Calls: []
--- !u!114 &1587012608
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1587012604}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b2bd544dc8bd07147b60d76c0c13a6f3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemIconImage: {fileID: 0}
  backgroundImage: {fileID: 1591538251}
  quantityText: {fileID: 293746993}
  parentUI: {fileID: 475421019}
  defaultBackgroundColor: {r: 1, g: 1, b: 1, a: 1}
  coveredBackgroundColor: {r: 0.8, g: 0.8, b: 0.8, a: 0.5}
  occupiedBackgroundColor: {r: 0.9, g: 0.9, b: 0.9, a: 1}
  slotIndexInInventory: 0
  CurrentModelPresenter: {fileID: 0}
  selectionHighlightImage: {fileID: 0}
--- !u!1 &1590002950
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1590002951}
  m_Layer: 4
  m_Name: Bottom Point
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1590002951
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1590002950}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: -0.3969, z: 0.21}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1606239611}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1591538249
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1591538250}
  - component: {fileID: 1591538252}
  - component: {fileID: 1591538251}
  m_Layer: 0
  m_Name: Image
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1591538250
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1591538249}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1587012605}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1591538251
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1591538249}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1591538252
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1591538249}
  m_CullTransparentMesh: 1
--- !u!1 &1606239607
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1606239611}
  - component: {fileID: 1606239610}
  - component: {fileID: 1606239609}
  - component: {fileID: 1606239608}
  - component: {fileID: 1606239612}
  m_Layer: 4
  m_Name: Cube
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!65 &1606239608
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1606239607}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &1606239609
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1606239607}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 31321ba15b8f8eb4c954353edc038b1d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1606239610
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1606239607}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!4 &1606239611
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1606239607}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 41.587708, y: 19.27444, z: -19.140701}
  m_LocalScale: {x: 22.52, y: 32.86, z: 10}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 67746911}
  - {fileID: 1590002951}
  m_Father: {fileID: 1233695913}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1606239612
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1606239607}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: af279b2945f36e14294dcb857761eaf5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  playersMono: []
  playerLayer:
    serializedVersion: 2
    m_Bits: 8
--- !u!1 &1625563411
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1625563412}
  m_Layer: 1
  m_Name: Camera Post Process
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1625563412
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1625563411}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 858718430}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1641252823
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1641252824}
  m_Layer: 0
  m_Name: Spawn Item
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1641252824
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1641252823}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: -24}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1668313608}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1 &1645645475
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1645645476}
  - component: {fileID: 1645645478}
  - component: {fileID: 1645645477}
  m_Layer: 0
  m_Name: Image
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1645645476
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1645645475}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 782819668}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1645645477
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1645645475}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1645645478
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1645645475}
  m_CullTransparentMesh: 1
--- !u!1 &1651308560
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1651308565}
  - component: {fileID: 1651308564}
  - component: {fileID: 1651308563}
  - component: {fileID: 1651308562}
  - component: {fileID: 1651308561}
  m_Layer: 4
  m_Name: Cube (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1651308561
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1651308560}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f2caaf903a6957f47bfcc7d2174481bb, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  playersMono: []
  playerLayer:
    serializedVersion: 2
    m_Bits: 0
--- !u!65 &1651308562
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1651308560}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &1651308563
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1651308560}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 31321ba15b8f8eb4c954353edc038b1d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1651308564
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1651308560}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!4 &1651308565
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1651308560}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 16.787706, y: 19.27444, z: -19.140701}
  m_LocalScale: {x: 22.52, y: 32.86, z: 10}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1718804134}
  - {fileID: 117214859}
  m_Father: {fileID: 1233695913}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1660565377
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1660565378}
  - component: {fileID: 1660565381}
  - component: {fileID: 1660565380}
  - component: {fileID: 1660565379}
  m_Layer: 0
  m_Name: Slot 2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1660565378
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1660565377}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 714248100}
  - {fileID: 1382638832}
  - {fileID: 1892822203}
  m_Father: {fileID: 687848106}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!225 &1660565379
CanvasGroup:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1660565377}
  m_Enabled: 1
  m_Alpha: 1
  m_Interactable: 1
  m_BlocksRaycasts: 1
  m_IgnoreParentGroups: 0
--- !u!114 &1660565380
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1660565377}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4e29b1a8efbd4b44bb3f3716e73f07ff, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.92993355, g: 1, b: 0, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 714248101}
  m_OnClick:
    m_PersistentCalls:
      m_Calls: []
--- !u!114 &1660565381
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1660565377}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b2bd544dc8bd07147b60d76c0c13a6f3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemIconImage: {fileID: 0}
  backgroundImage: {fileID: 714248101}
  quantityText: {fileID: 319518063}
  parentUI: {fileID: 475421019}
  defaultBackgroundColor: {r: 1, g: 1, b: 1, a: 1}
  coveredBackgroundColor: {r: 0.8, g: 0.8, b: 0.8, a: 0.5}
  occupiedBackgroundColor: {r: 0.9, g: 0.9, b: 0.9, a: 1}
  slotIndexInInventory: 0
  CurrentModelPresenter: {fileID: 0}
  selectionHighlightImage: {fileID: 0}
--- !u!1 &1668313607
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1668313608}
  - component: {fileID: 1668313611}
  - component: {fileID: 1668313610}
  - component: {fileID: 1668313609}
  m_Layer: 0
  m_Name: Slot 15
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1668313608
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1668313607}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1914595679}
  - {fileID: 1641252824}
  - {fileID: 2042462911}
  m_Father: {fileID: 687848106}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!225 &1668313609
CanvasGroup:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1668313607}
  m_Enabled: 1
  m_Alpha: 1
  m_Interactable: 1
  m_BlocksRaycasts: 1
  m_IgnoreParentGroups: 0
--- !u!114 &1668313610
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1668313607}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4e29b1a8efbd4b44bb3f3716e73f07ff, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.92993355, g: 1, b: 0, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 1914595680}
  m_OnClick:
    m_PersistentCalls:
      m_Calls: []
--- !u!114 &1668313611
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1668313607}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b2bd544dc8bd07147b60d76c0c13a6f3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemIconImage: {fileID: 0}
  backgroundImage: {fileID: 1914595680}
  quantityText: {fileID: 1445526242}
  parentUI: {fileID: 475421019}
  defaultBackgroundColor: {r: 1, g: 1, b: 1, a: 1}
  coveredBackgroundColor: {r: 0.8, g: 0.8, b: 0.8, a: 0.5}
  occupiedBackgroundColor: {r: 0.9, g: 0.9, b: 0.9, a: 1}
  slotIndexInInventory: 0
  CurrentModelPresenter: {fileID: 0}
  selectionHighlightImage: {fileID: 0}
--- !u!1 &1678824428
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1678824429}
  - component: {fileID: 1678824431}
  - component: {fileID: 1678824430}
  m_Layer: 0
  m_Name: Text (TMP)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1678824429
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1678824428}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1899777960}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 1, y: 0}
  m_AnchorMax: {x: 1, y: 0}
  m_AnchoredPosition: {x: -18.6604, y: 5.2325}
  m_SizeDelta: {x: 37.3207, y: 9.9206}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1678824430
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1678824428}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: 
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_sharedMaterial: {fileID: 2180264, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4278190335
  m_fontColor: {r: 1, g: 0, b: 0, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 36
  m_fontSizeBase: 36
  m_fontWeight: 400
  m_enableAutoSizing: 1
  m_fontSizeMin: 1
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 4
  m_VerticalAlignment: 1024
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 0
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!222 &1678824431
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1678824428}
  m_CullTransparentMesh: 1
--- !u!1 &1709965263
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1709965264}
  m_Layer: 0
  m_Name: Spawn Item
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1709965264
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1709965263}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: -24}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: *********}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1 &1717622682
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1717622683}
  - component: {fileID: 1717622685}
  - component: {fileID: 1717622684}
  m_Layer: 0
  m_Name: Text (TMP)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1717622683
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1717622682}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 733408586}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 1, y: 0}
  m_AnchorMax: {x: 1, y: 0}
  m_AnchoredPosition: {x: -18.6604, y: 5.2325}
  m_SizeDelta: {x: 37.3207, y: 9.9206}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1717622684
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1717622682}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: 
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_sharedMaterial: {fileID: 2180264, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4278190335
  m_fontColor: {r: 1, g: 0, b: 0, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 36
  m_fontSizeBase: 36
  m_fontWeight: 400
  m_enableAutoSizing: 1
  m_fontSizeMin: 1
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 4
  m_VerticalAlignment: 1024
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 0
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!222 &1717622685
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1717622682}
  m_CullTransparentMesh: 1
--- !u!1 &1718804133
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1718804134}
  m_Layer: 4
  m_Name: Top Point
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1718804134
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1718804133}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0.506, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1651308565}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1756558041
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1756558043}
  - component: {fileID: 1756558042}
  m_Layer: 6
  m_Name: Scene Data Holder
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1756558042
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1756558041}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e4f7e1b103cc4f348adf7c950b0aeaba, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Name: 
  BuildIndex: 0
  InputMapName: Player
  objectsToSave: []
  objectsToSaveData: []
  PlayerAddress:
    m_AssetGUID: 
    m_SubObjectName: 
    m_SubObjectType: 
    m_SubObjectGUID: 
    m_EditorAssetChanged: 0
  ActivedScene:
    m_Handle: 0
  spawnPlayersOnLoad: 1
  spawnPointManager: {fileID: 0}
--- !u!4 &1756558043
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1756558041}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 35.812717, y: 11.599472, z: -36.102524}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1763709972
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1763709973}
  - component: {fileID: 1763709975}
  - component: {fileID: 1763709974}
  m_Layer: 0
  m_Name: Image
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1763709973
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1763709972}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 161268398}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1763709974
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1763709972}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1763709975
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1763709972}
  m_CullTransparentMesh: 1
--- !u!1 &1773055872
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1773055873}
  m_Layer: 0
  m_Name: Sliding Area
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1773055873
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1773055872}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1944773584}
  m_Father: {fileID: 1281470156}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: -20, y: -20}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1 &1773715523
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1773715524}
  m_Layer: 0
  m_Name: Spawn Item
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1773715524
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1773715523}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: -24}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1165677728}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1 &1778917172
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1778917173}
  - component: {fileID: 1778917175}
  - component: {fileID: 1778917174}
  m_Layer: 0
  m_Name: Image
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1778917173
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1778917172}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1912301149}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1778917174
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1778917172}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1778917175
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1778917172}
  m_CullTransparentMesh: 1
--- !u!1 &1864769040
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1864769041}
  - component: {fileID: 1864769043}
  - component: {fileID: 1864769042}
  m_Layer: 0
  m_Name: Text (TMP)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1864769041
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1864769040}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 779154122}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 1, y: 0}
  m_AnchorMax: {x: 1, y: 0}
  m_AnchoredPosition: {x: -18.6604, y: 5.2325}
  m_SizeDelta: {x: 37.3207, y: 9.9206}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1864769042
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1864769040}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: 
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_sharedMaterial: {fileID: 2180264, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4278190335
  m_fontColor: {r: 1, g: 0, b: 0, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 36
  m_fontSizeBase: 36
  m_fontWeight: 400
  m_enableAutoSizing: 1
  m_fontSizeMin: 1
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 4
  m_VerticalAlignment: 1024
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 0
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!222 &1864769043
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1864769040}
  m_CullTransparentMesh: 1
--- !u!114 &1875963268 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 5543666884352293257, guid: 4fceb36ebeeaae24e9dc6a79ee9c7607, type: 3}
  m_PrefabInstance: {fileID: 4455869690388524289}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a4b13271c5eea464c927648efab5e6f1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &1875963270 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 6723813748762121387, guid: 4fceb36ebeeaae24e9dc6a79ee9c7607, type: 3}
  m_PrefabInstance: {fileID: 4455869690388524289}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8d10aae02c89af54a844e70b4d87f45d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &1892822202
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1892822203}
  - component: {fileID: 1892822205}
  - component: {fileID: 1892822204}
  m_Layer: 0
  m_Name: Icon
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1892822203
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1892822202}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 319518062}
  m_Father: {fileID: 1660565378}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: -20, y: -20}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1892822204
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1892822202}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0, g: 1, b: 0.8949728, a: 0.45490196}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1892822205
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1892822202}
  m_CullTransparentMesh: 1
--- !u!1 &1899777959
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1899777960}
  - component: {fileID: 1899777962}
  - component: {fileID: 1899777961}
  m_Layer: 0
  m_Name: Icon
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1899777960
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1899777959}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1678824429}
  m_Father: {fileID: 1165677728}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: -20, y: -20}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1899777961
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1899777959}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0, g: 1, b: 0.8949728, a: 0.45490196}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1899777962
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1899777959}
  m_CullTransparentMesh: 1
--- !u!1 &1912301148
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1912301149}
  - component: {fileID: 1912301152}
  - component: {fileID: 1912301151}
  - component: {fileID: 1912301150}
  m_Layer: 0
  m_Name: Slot 14
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1912301149
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1912301148}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1778917173}
  - {fileID: 168342585}
  - {fileID: 190675093}
  m_Father: {fileID: 687848106}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!225 &1912301150
CanvasGroup:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1912301148}
  m_Enabled: 1
  m_Alpha: 1
  m_Interactable: 1
  m_BlocksRaycasts: 1
  m_IgnoreParentGroups: 0
--- !u!114 &1912301151
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1912301148}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4e29b1a8efbd4b44bb3f3716e73f07ff, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.92993355, g: 1, b: 0, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 1778917174}
  m_OnClick:
    m_PersistentCalls:
      m_Calls: []
--- !u!114 &1912301152
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1912301148}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b2bd544dc8bd07147b60d76c0c13a6f3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemIconImage: {fileID: 0}
  backgroundImage: {fileID: 1778917174}
  quantityText: {fileID: 1993188519}
  parentUI: {fileID: 475421019}
  defaultBackgroundColor: {r: 1, g: 1, b: 1, a: 1}
  coveredBackgroundColor: {r: 0.8, g: 0.8, b: 0.8, a: 0.5}
  occupiedBackgroundColor: {r: 0.9, g: 0.9, b: 0.9, a: 1}
  slotIndexInInventory: 0
  CurrentModelPresenter: {fileID: 0}
  selectionHighlightImage: {fileID: 0}
--- !u!1 &1914595678
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1914595679}
  - component: {fileID: 1914595681}
  - component: {fileID: 1914595680}
  m_Layer: 0
  m_Name: Image
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1914595679
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1914595678}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1668313608}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1914595680
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1914595678}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1914595681
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1914595678}
  m_CullTransparentMesh: 1
--- !u!1 &1936227666
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1936227667}
  - component: {fileID: 1936227670}
  - component: {fileID: 1936227669}
  - component: {fileID: 1936227668}
  m_Layer: 0
  m_Name: Viewport
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1936227667
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1936227666}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 687848106}
  m_Father: {fileID: 1471678706}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0, y: 1}
--- !u!114 &1936227668
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1936227666}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 31a19414c41e5ae4aae2af33fee712f6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_ShowMaskGraphic: 0
--- !u!114 &1936227669
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1936227666}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 10917, guid: 0000000000000000f000000000000000, type: 0}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1936227670
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1936227666}
  m_CullTransparentMesh: 1
--- !u!1 &1944773583
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1944773584}
  - component: {fileID: 1944773586}
  - component: {fileID: 1944773585}
  m_Layer: 0
  m_Name: Handle
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1944773584
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1944773583}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1773055873}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 20, y: 20}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1944773585
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1944773583}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 10905, guid: 0000000000000000f000000000000000, type: 0}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1944773586
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1944773583}
  m_CullTransparentMesh: 1
--- !u!1 &1950327572
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1950327573}
  m_Layer: 0
  m_Name: Spawn Item
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1950327573
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1950327572}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: -24}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1587012605}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1 &1960457736
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1960457737}
  m_Layer: 0
  m_Name: Sliding Area
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1960457737
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1960457736}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1169943727}
  m_Father: {fileID: 1525167452}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: -20, y: -20}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1 &1962102714
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1962102720}
  - component: {fileID: 1962102719}
  - component: {fileID: 1962102718}
  - component: {fileID: 1962102717}
  - component: {fileID: 1962102716}
  - component: {fileID: 1962102715}
  m_Layer: 5
  m_Name: Canvas
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!222 &1962102715
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1962102714}
  m_CullTransparentMesh: 1
--- !u!114 &1962102716
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1962102714}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 24884b6f57ccae54a9a41b4d40162e2a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  documento: {fileID: 0}
  textDisplayer: {fileID: 316418721}
--- !u!114 &1962102717
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1962102714}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc42784cf147c0c48a680349fa168899, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IgnoreReversedGraphics: 1
  m_BlockingObjects: 0
  m_BlockingMask:
    serializedVersion: 2
    m_Bits: 4294967295
--- !u!114 &1962102718
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1962102714}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0cd44c1031e13a943bb63640046fad76, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UiScaleMode: 0
  m_ReferencePixelsPerUnit: 100
  m_ScaleFactor: 1
  m_ReferenceResolution: {x: 800, y: 600}
  m_ScreenMatchMode: 0
  m_MatchWidthOrHeight: 0
  m_PhysicalUnit: 3
  m_FallbackScreenDPI: 96
  m_DefaultSpriteDPI: 96
  m_DynamicPixelsPerUnit: 1
  m_PresetInfoIsWorld: 0
--- !u!223 &1962102719
Canvas:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1962102714}
  m_Enabled: 1
  serializedVersion: 3
  m_RenderMode: 0
  m_Camera: {fileID: 0}
  m_PlaneDistance: 100
  m_PixelPerfect: 0
  m_ReceivesEvents: 1
  m_OverrideSorting: 0
  m_OverridePixelPerfect: 0
  m_SortingBucketNormalizedSize: 0
  m_VertexColorAlwaysGammaSpace: 0
  m_AdditionalShaderChannelsFlag: 25
  m_UpdateRectTransformForStandalone: 0
  m_SortingLayerID: 0
  m_SortingOrder: 0
  m_TargetDisplay: 0
--- !u!224 &1962102720
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1962102714}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0, y: 0, z: 0}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1042441660}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0, y: 0}
--- !u!1 &1976096063
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1976096064}
  - component: {fileID: 1976096066}
  - component: {fileID: 1976096065}
  m_Layer: 0
  m_Name: Image
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1976096064
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1976096063}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 481624697}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1976096065
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1976096063}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1976096066
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1976096063}
  m_CullTransparentMesh: 1
--- !u!1 &1980274881
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1980274882}
  - component: {fileID: 1980274884}
  - component: {fileID: 1980274883}
  m_Layer: 0
  m_Name: Image
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1980274882
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1980274881}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 391214796}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1980274883
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1980274881}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1980274884
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1980274881}
  m_CullTransparentMesh: 1
--- !u!1 &1993188517
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1993188518}
  - component: {fileID: 1993188520}
  - component: {fileID: 1993188519}
  m_Layer: 0
  m_Name: Text (TMP)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1993188518
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1993188517}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 190675093}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 1, y: 0}
  m_AnchorMax: {x: 1, y: 0}
  m_AnchoredPosition: {x: -18.6604, y: 5.2325}
  m_SizeDelta: {x: 37.3207, y: 9.9206}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1993188519
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1993188517}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: 
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_sharedMaterial: {fileID: 2180264, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4278190335
  m_fontColor: {r: 1, g: 0, b: 0, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 36
  m_fontSizeBase: 36
  m_fontWeight: 400
  m_enableAutoSizing: 1
  m_fontSizeMin: 1
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 4
  m_VerticalAlignment: 1024
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 0
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!222 &1993188520
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1993188517}
  m_CullTransparentMesh: 1
--- !u!1 &2006396557
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2006396558}
  - component: {fileID: 2006396561}
  - component: {fileID: 2006396560}
  - component: {fileID: 2006396559}
  m_Layer: 0
  m_Name: Slot 1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &2006396558
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2006396557}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 392111199}
  - {fileID: 606755174}
  - {fileID: 444281375}
  m_Father: {fileID: 687848106}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!225 &2006396559
CanvasGroup:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2006396557}
  m_Enabled: 1
  m_Alpha: 1
  m_Interactable: 1
  m_BlocksRaycasts: 1
  m_IgnoreParentGroups: 0
--- !u!114 &2006396560
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2006396557}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4e29b1a8efbd4b44bb3f3716e73f07ff, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.92993355, g: 1, b: 0, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 392111200}
  m_OnClick:
    m_PersistentCalls:
      m_Calls: []
--- !u!114 &2006396561
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2006396557}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b2bd544dc8bd07147b60d76c0c13a6f3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemIconImage: {fileID: 0}
  backgroundImage: {fileID: 392111200}
  quantityText: {fileID: 61628570}
  parentUI: {fileID: 475421019}
  defaultBackgroundColor: {r: 1, g: 1, b: 1, a: 1}
  coveredBackgroundColor: {r: 0.8, g: 0.8, b: 0.8, a: 0.5}
  occupiedBackgroundColor: {r: 0.9, g: 0.9, b: 0.9, a: 1}
  slotIndexInInventory: 0
  CurrentModelPresenter: {fileID: 0}
  selectionHighlightImage: {fileID: 0}
--- !u!1 &2016954411
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2016954412}
  - component: {fileID: 2016954414}
  - component: {fileID: 2016954413}
  m_Layer: 0
  m_Name: Icon
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &2016954412
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2016954411}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 658101684}
  m_Father: {fileID: 481624697}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: -20, y: -20}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &2016954413
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2016954411}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0, g: 1, b: 0.8949728, a: 0.45490196}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &2016954414
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2016954411}
  m_CullTransparentMesh: 1
--- !u!1 &2042462910
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2042462911}
  - component: {fileID: 2042462913}
  - component: {fileID: 2042462912}
  m_Layer: 0
  m_Name: Icon
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &2042462911
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2042462910}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1445526241}
  m_Father: {fileID: 1668313608}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: -20, y: -20}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &2042462912
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2042462910}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0, g: 1, b: 0.8949728, a: 0.45490196}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &2042462913
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2042462910}
  m_CullTransparentMesh: 1
--- !u!1 &2044205515
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2044205516}
  - component: {fileID: 2044205519}
  - component: {fileID: 2044205518}
  - component: {fileID: 2044205517}
  m_Layer: 0
  m_Name: Slot 11
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &2044205516
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2044205515}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1060004810}
  - {fileID: 601210455}
  - {fileID: 296346163}
  m_Father: {fileID: 687848106}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!225 &2044205517
CanvasGroup:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2044205515}
  m_Enabled: 1
  m_Alpha: 1
  m_Interactable: 1
  m_BlocksRaycasts: 1
  m_IgnoreParentGroups: 0
--- !u!114 &2044205518
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2044205515}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4e29b1a8efbd4b44bb3f3716e73f07ff, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.92993355, g: 1, b: 0, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 1060004811}
  m_OnClick:
    m_PersistentCalls:
      m_Calls: []
--- !u!114 &2044205519
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2044205515}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b2bd544dc8bd07147b60d76c0c13a6f3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemIconImage: {fileID: 0}
  backgroundImage: {fileID: 1060004811}
  quantityText: {fileID: 249105724}
  parentUI: {fileID: 475421019}
  defaultBackgroundColor: {r: 1, g: 1, b: 1, a: 1}
  coveredBackgroundColor: {r: 0.8, g: 0.8, b: 0.8, a: 0.5}
  occupiedBackgroundColor: {r: 0.9, g: 0.9, b: 0.9, a: 1}
  slotIndexInInventory: 0
  CurrentModelPresenter: {fileID: 0}
  selectionHighlightImage: {fileID: 0}
--- !u!1 &2044630219
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2044630220}
  m_Layer: 0
  m_Name: Spawn Item
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &2044630220
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2044630219}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: -24}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 481624697}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1 &2109280226 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 2768980298636603119, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
  m_PrefabInstance: {fileID: 3504528070976578228}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &2109280228
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2109280226}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0783879a345b80e4eb1478d837d0b7e5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Bag: {fileID: 325580531}
  targetInventory: {fileID: 858718443}
  inventoryPanel: {fileID: 212244598}
  inventorySlotPrefab: {fileID: 5553402093153446677, guid: 760c1ab5ae139b34d8a3f2d03de344db, type: 3}
  slotPanel: {fileID: 1486144099}
  HotbarPanel: {fileID: 0}
  itemModelPresenterPrefab: {fileID: 2089220023221089146, guid: 43f109199fa2ac649893710efb217140, type: 3}
  modelPresenterParent: {fileID: 1486144099}
  defaultModelOffset: {x: 0, y: 0, z: 0}
  defaultModelScaleFactor: 1
  playerCharacterTransform: {fileID: 858718430}
  validPlacementColor: {r: 0, g: 1, b: 0, a: 0.3}
  invalidPlacementColor: {r: 1, g: 0, b: 0, a: 0.3}
  MOUSE_ROTATE_ACTION: {fileID: 0}
  gamepadSelectedSlotHighlightColor: {r: 1, g: 0.92, b: 0.016, a: 0.5}
  GAMEPAD_NAVIGATE_ACTION: {fileID: 0}
  GAMEPAD_SELECT_ACTION: {fileID: 0}
  GAMEPAD_ROTATE_ACTION: {fileID: 0}
  GAMEPAD_CANCEL_ACTION: {fileID: 0}
--- !u!223 &2109280229 stripped
Canvas:
  m_CorrespondingSourceObject: {fileID: 2411850248514626195, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
  m_PrefabInstance: {fileID: 3504528070976578228}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &2109280230
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2109280226}
  m_Enabled: 0
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f3b9f83b3df505441a0a91f16f1ae084, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  targetObject: {fileID: 325580531}
  targetCanvas: {fileID: 2109280229}
  offsetDistance: 0.01
  padding: 0.2
--- !u!114 &2109280231
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2109280226}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc42784cf147c0c48a680349fa168899, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IgnoreReversedGraphics: 1
  m_BlockingObjects: 0
  m_BlockingMask:
    serializedVersion: 2
    m_Bits: 4294967295
--- !u!1 &2115109467
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2115109471}
  - component: {fileID: 2115109470}
  - component: {fileID: 2115109469}
  - component: {fileID: 2115109468}
  - component: {fileID: 2115109473}
  m_Layer: 0
  m_Name: Cube
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!65 &2115109468
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2115109467}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 32
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &2115109469
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2115109467}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 31321ba15b8f8eb4c954353edc038b1d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &2115109470
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2115109467}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!4 &2115109471
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2115109467}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: 0.99935204, z: -0, w: 0.035992865}
  m_LocalPosition: {x: 9.69, y: 1.47, z: 12.7}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 475421021}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 175.875, z: 0}
--- !u!54 &2115109473
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2115109467}
  serializedVersion: 4
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 1
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!1 &2137849846
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2137849847}
  - component: {fileID: 2137849849}
  - component: {fileID: 2137849848}
  m_Layer: 0
  m_Name: Text (TMP)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &2137849847
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2137849846}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 38409046}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 1, y: 0}
  m_AnchorMax: {x: 1, y: 0}
  m_AnchoredPosition: {x: -18.6604, y: 5.2325}
  m_SizeDelta: {x: 37.3207, y: 9.9206}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &2137849848
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2137849846}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: 
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_sharedMaterial: {fileID: 2180264, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4278190335
  m_fontColor: {r: 1, g: 0, b: 0, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 36
  m_fontSizeBase: 36
  m_fontWeight: 400
  m_enableAutoSizing: 1
  m_fontSizeMin: 1
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 4
  m_VerticalAlignment: 1024
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 0
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!222 &2137849849
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2137849846}
  m_CullTransparentMesh: 1
--- !u!1001 &1666664377295561245
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: -5778075969088957238, guid: c5567364c5ebcf441b0106b7017b2d9c, type: 3}
      propertyPath: action.m_Name
      value: Action
      objectReference: {fileID: 0}
    - target: {fileID: -5778075969088957238, guid: c5567364c5ebcf441b0106b7017b2d9c, type: 3}
      propertyPath: examinePanelPrefab
      value: 
      objectReference: {fileID: 9014103880887437540, guid: b0444ab19c001784181726f095c3eb8c, type: 3}
    - target: {fileID: -5778075969088957238, guid: c5567364c5ebcf441b0106b7017b2d9c, type: 3}
      propertyPath: action.m_ExpectedControlType
      value: Vector2
      objectReference: {fileID: 0}
    - target: {fileID: -5778075969088957238, guid: c5567364c5ebcf441b0106b7017b2d9c, type: 3}
      propertyPath: visualInteractables.Array.size
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -5778075969088957238, guid: c5567364c5ebcf441b0106b7017b2d9c, type: 3}
      propertyPath: InteractBinding.InputInteraction
      value: -2
      objectReference: {fileID: 0}
    - target: {fileID: -5778075969088957238, guid: c5567364c5ebcf441b0106b7017b2d9c, type: 3}
      propertyPath: 'managedReferences[2854501665421656152]'
      value: Assembly-CSharp HoldingTimer
      objectReference: {fileID: 0}
    - target: {fileID: -5778075969088957238, guid: c5567364c5ebcf441b0106b7017b2d9c, type: 3}
      propertyPath: 'managedReferences[2854501665421656153]'
      value: Unity.InputSystem UnityEngine.InputSystem.Interactions.MultiTapInteraction
      objectReference: {fileID: 0}
    - target: {fileID: -5778075969088957238, guid: c5567364c5ebcf441b0106b7017b2d9c, type: 3}
      propertyPath: 'managedReferences[2854501665421656154]'
      value: Unity.InputSystem UnityEngine.InputSystem.Interactions.PressInteraction
      objectReference: {fileID: 0}
    - target: {fileID: -5778075969088957238, guid: c5567364c5ebcf441b0106b7017b2d9c, type: 3}
      propertyPath: action.m_SingletonActionBindings.Array.size
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -5778075969088957238, guid: c5567364c5ebcf441b0106b7017b2d9c, type: 3}
      propertyPath: managedReferences[2854501665421656153].tapTime
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -5778075969088957238, guid: c5567364c5ebcf441b0106b7017b2d9c, type: 3}
      propertyPath: managedReferences[2854501665421656153].tapCount
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: -5778075969088957238, guid: c5567364c5ebcf441b0106b7017b2d9c, type: 3}
      propertyPath: managedReferences[2854501665421656153].tapDelay
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -5778075969088957238, guid: c5567364c5ebcf441b0106b7017b2d9c, type: 3}
      propertyPath: managedReferences[2854501665421656154].behavior
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -5778075969088957238, guid: c5567364c5ebcf441b0106b7017b2d9c, type: 3}
      propertyPath: managedReferences[2854501665421656153].pressPoint
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -5778075969088957238, guid: c5567364c5ebcf441b0106b7017b2d9c, type: 3}
      propertyPath: managedReferences[2854501665421656154].pressPoint
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -5778075969088957238, guid: c5567364c5ebcf441b0106b7017b2d9c, type: 3}
      propertyPath: managedReferences[2854501665421656152].holdingTime
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -5778075969088957238, guid: c5567364c5ebcf441b0106b7017b2d9c, type: 3}
      propertyPath: action.m_SingletonActionBindings.Array.data[0].m_Id
      value: af4617c0-2ddf-4a25-911d-2f09d59cd025
      objectReference: {fileID: 0}
    - target: {fileID: -5778075969088957238, guid: c5567364c5ebcf441b0106b7017b2d9c, type: 3}
      propertyPath: action.m_SingletonActionBindings.Array.data[1].m_Id
      value: 1624af16-e315-4d58-b012-5ab00933d69d
      objectReference: {fileID: 0}
    - target: {fileID: -5778075969088957238, guid: c5567364c5ebcf441b0106b7017b2d9c, type: 3}
      propertyPath: action.m_SingletonActionBindings.Array.data[2].m_Id
      value: 7879410d-68ea-4486-a0f9-b262d4cf2e41
      objectReference: {fileID: 0}
    - target: {fileID: -5778075969088957238, guid: c5567364c5ebcf441b0106b7017b2d9c, type: 3}
      propertyPath: action.m_SingletonActionBindings.Array.data[3].m_Id
      value: 943d8fea-dd2f-4fb2-a75f-aefcfd7b279d
      objectReference: {fileID: 0}
    - target: {fileID: -5778075969088957238, guid: c5567364c5ebcf441b0106b7017b2d9c, type: 3}
      propertyPath: action.m_SingletonActionBindings.Array.data[4].m_Id
      value: 325d4701-6aca-4990-9af2-f262d8dde4c9
      objectReference: {fileID: 0}
    - target: {fileID: -5778075969088957238, guid: c5567364c5ebcf441b0106b7017b2d9c, type: 3}
      propertyPath: action.m_SingletonActionBindings.Array.data[0].m_Name
      value: 2D Vector
      objectReference: {fileID: 0}
    - target: {fileID: -5778075969088957238, guid: c5567364c5ebcf441b0106b7017b2d9c, type: 3}
      propertyPath: action.m_SingletonActionBindings.Array.data[0].m_Path
      value: 2DVector
      objectReference: {fileID: 0}
    - target: {fileID: -5778075969088957238, guid: c5567364c5ebcf441b0106b7017b2d9c, type: 3}
      propertyPath: action.m_SingletonActionBindings.Array.data[1].m_Name
      value: up
      objectReference: {fileID: 0}
    - target: {fileID: -5778075969088957238, guid: c5567364c5ebcf441b0106b7017b2d9c, type: 3}
      propertyPath: action.m_SingletonActionBindings.Array.data[2].m_Name
      value: down
      objectReference: {fileID: 0}
    - target: {fileID: -5778075969088957238, guid: c5567364c5ebcf441b0106b7017b2d9c, type: 3}
      propertyPath: action.m_SingletonActionBindings.Array.data[3].m_Name
      value: left
      objectReference: {fileID: 0}
    - target: {fileID: -5778075969088957238, guid: c5567364c5ebcf441b0106b7017b2d9c, type: 3}
      propertyPath: action.m_SingletonActionBindings.Array.data[4].m_Name
      value: right
      objectReference: {fileID: 0}
    - target: {fileID: -5778075969088957238, guid: c5567364c5ebcf441b0106b7017b2d9c, type: 3}
      propertyPath: action.m_SingletonActionBindings.Array.data[0].m_Flags
      value: 4
      objectReference: {fileID: 0}
    - target: {fileID: -5778075969088957238, guid: c5567364c5ebcf441b0106b7017b2d9c, type: 3}
      propertyPath: action.m_SingletonActionBindings.Array.data[1].m_Flags
      value: 8
      objectReference: {fileID: 0}
    - target: {fileID: -5778075969088957238, guid: c5567364c5ebcf441b0106b7017b2d9c, type: 3}
      propertyPath: action.m_SingletonActionBindings.Array.data[2].m_Flags
      value: 8
      objectReference: {fileID: 0}
    - target: {fileID: -5778075969088957238, guid: c5567364c5ebcf441b0106b7017b2d9c, type: 3}
      propertyPath: action.m_SingletonActionBindings.Array.data[3].m_Flags
      value: 8
      objectReference: {fileID: 0}
    - target: {fileID: -5778075969088957238, guid: c5567364c5ebcf441b0106b7017b2d9c, type: 3}
      propertyPath: action.m_SingletonActionBindings.Array.data[4].m_Flags
      value: 8
      objectReference: {fileID: 0}
    - target: {fileID: -5778075969088957238, guid: c5567364c5ebcf441b0106b7017b2d9c, type: 3}
      propertyPath: action.m_SingletonActionBindings.Array.data[0].m_Action
      value: Action
      objectReference: {fileID: 0}
    - target: {fileID: -5778075969088957238, guid: c5567364c5ebcf441b0106b7017b2d9c, type: 3}
      propertyPath: action.m_SingletonActionBindings.Array.data[1].m_Action
      value: Action
      objectReference: {fileID: 0}
    - target: {fileID: -5778075969088957238, guid: c5567364c5ebcf441b0106b7017b2d9c, type: 3}
      propertyPath: action.m_SingletonActionBindings.Array.data[2].m_Action
      value: Action
      objectReference: {fileID: 0}
    - target: {fileID: -5778075969088957238, guid: c5567364c5ebcf441b0106b7017b2d9c, type: 3}
      propertyPath: action.m_SingletonActionBindings.Array.data[3].m_Action
      value: Action
      objectReference: {fileID: 0}
    - target: {fileID: -5778075969088957238, guid: c5567364c5ebcf441b0106b7017b2d9c, type: 3}
      propertyPath: action.m_SingletonActionBindings.Array.data[4].m_Action
      value: Action
      objectReference: {fileID: 0}
    - target: {fileID: -5778075969088957238, guid: c5567364c5ebcf441b0106b7017b2d9c, type: 3}
      propertyPath: action.m_SingletonActionBindings.Array.data[0].m_Interactions
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 7260997104285542664, guid: c5567364c5ebcf441b0106b7017b2d9c, type: 3}
      propertyPath: m_LocalPosition.x
      value: 4.9807363
      objectReference: {fileID: 0}
    - target: {fileID: 7260997104285542664, guid: c5567364c5ebcf441b0106b7017b2d9c, type: 3}
      propertyPath: m_LocalPosition.y
      value: 1.65
      objectReference: {fileID: 0}
    - target: {fileID: 7260997104285542664, guid: c5567364c5ebcf441b0106b7017b2d9c, type: 3}
      propertyPath: m_LocalPosition.z
      value: 11.015077
      objectReference: {fileID: 0}
    - target: {fileID: 7260997104285542664, guid: c5567364c5ebcf441b0106b7017b2d9c, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 7260997104285542664, guid: c5567364c5ebcf441b0106b7017b2d9c, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7260997104285542664, guid: c5567364c5ebcf441b0106b7017b2d9c, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7260997104285542664, guid: c5567364c5ebcf441b0106b7017b2d9c, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7260997104285542664, guid: c5567364c5ebcf441b0106b7017b2d9c, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7260997104285542664, guid: c5567364c5ebcf441b0106b7017b2d9c, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7260997104285542664, guid: c5567364c5ebcf441b0106b7017b2d9c, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8038906410268111794, guid: c5567364c5ebcf441b0106b7017b2d9c, type: 3}
      propertyPath: m_Name
      value: shovel
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents:
    - targetCorrespondingSourceObject: {fileID: 8038906410268111794, guid: c5567364c5ebcf441b0106b7017b2d9c, type: 3}
      insertIndex: -1
      addedObject: {fileID: 1034575649}
  m_SourcePrefab: {fileID: 100100000, guid: c5567364c5ebcf441b0106b7017b2d9c, type: 3}
--- !u!1001 &1780177147944756748
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: -7482407542692310298, guid: 2baa18faa2ec17a46bb3e89286eb3d85, type: 3}
      propertyPath: action.m_Name
      value: Action
      objectReference: {fileID: 0}
    - target: {fileID: -7482407542692310298, guid: 2baa18faa2ec17a46bb3e89286eb3d85, type: 3}
      propertyPath: examinePanelPrefab
      value: 
      objectReference: {fileID: 9014103880887437540, guid: b0444ab19c001784181726f095c3eb8c, type: 3}
    - target: {fileID: 7276070352826945420, guid: 2baa18faa2ec17a46bb3e89286eb3d85, type: 3}
      propertyPath: m_LocalPosition.x
      value: -1.99
      objectReference: {fileID: 0}
    - target: {fileID: 7276070352826945420, guid: 2baa18faa2ec17a46bb3e89286eb3d85, type: 3}
      propertyPath: m_LocalPosition.y
      value: 1.86
      objectReference: {fileID: 0}
    - target: {fileID: 7276070352826945420, guid: 2baa18faa2ec17a46bb3e89286eb3d85, type: 3}
      propertyPath: m_LocalPosition.z
      value: 15.565739
      objectReference: {fileID: 0}
    - target: {fileID: 7276070352826945420, guid: 2baa18faa2ec17a46bb3e89286eb3d85, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 7276070352826945420, guid: 2baa18faa2ec17a46bb3e89286eb3d85, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7276070352826945420, guid: 2baa18faa2ec17a46bb3e89286eb3d85, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7276070352826945420, guid: 2baa18faa2ec17a46bb3e89286eb3d85, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7276070352826945420, guid: 2baa18faa2ec17a46bb3e89286eb3d85, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7276070352826945420, guid: 2baa18faa2ec17a46bb3e89286eb3d85, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7276070352826945420, guid: 2baa18faa2ec17a46bb3e89286eb3d85, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8048526008928703798, guid: 2baa18faa2ec17a46bb3e89286eb3d85, type: 3}
      propertyPath: m_Name
      value: building_01
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 2baa18faa2ec17a46bb3e89286eb3d85, type: 3}
--- !u!1001 &2476892175714858251
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 2414519673451398432, guid: c5aaa7959394065408a6fbd6d21a4a14, type: 3}
      propertyPath: m_Name
      value: Garbage Bag
      objectReference: {fileID: 0}
    - target: {fileID: 3083215825653669786, guid: c5aaa7959394065408a6fbd6d21a4a14, type: 3}
      propertyPath: m_LocalPosition.x
      value: 5.44
      objectReference: {fileID: 0}
    - target: {fileID: 3083215825653669786, guid: c5aaa7959394065408a6fbd6d21a4a14, type: 3}
      propertyPath: m_LocalPosition.y
      value: 1.9
      objectReference: {fileID: 0}
    - target: {fileID: 3083215825653669786, guid: c5aaa7959394065408a6fbd6d21a4a14, type: 3}
      propertyPath: m_LocalPosition.z
      value: 3.0997515
      objectReference: {fileID: 0}
    - target: {fileID: 3083215825653669786, guid: c5aaa7959394065408a6fbd6d21a4a14, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 3083215825653669786, guid: c5aaa7959394065408a6fbd6d21a4a14, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3083215825653669786, guid: c5aaa7959394065408a6fbd6d21a4a14, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3083215825653669786, guid: c5aaa7959394065408a6fbd6d21a4a14, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3083215825653669786, guid: c5aaa7959394065408a6fbd6d21a4a14, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3083215825653669786, guid: c5aaa7959394065408a6fbd6d21a4a14, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3083215825653669786, guid: c5aaa7959394065408a6fbd6d21a4a14, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6874531462823371628, guid: c5aaa7959394065408a6fbd6d21a4a14, type: 3}
      propertyPath: action.m_Name
      value: Action
      objectReference: {fileID: 0}
    - target: {fileID: 6874531462823371628, guid: c5aaa7959394065408a6fbd6d21a4a14, type: 3}
      propertyPath: examinePanelPrefab
      value: 
      objectReference: {fileID: 9014103880887437540, guid: b0444ab19c001784181726f095c3eb8c, type: 3}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: c5aaa7959394065408a6fbd6d21a4a14, type: 3}
--- !u!1001 &3504528070976578228
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 325580535}
    m_Modifications:
    - target: {fileID: 222269441165859768, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      propertyPath: m_AnchorMax.x
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 222269441165859768, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 222269441165859768, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      propertyPath: m_AnchorMin.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 222269441165859768, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 222269441165859768, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 222269441165859768, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      propertyPath: m_SizeDelta.y
      value: -0.000015258789
      objectReference: {fileID: 0}
    - target: {fileID: 222269441165859768, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 222269441165859768, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 414565781989209798, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 414565781989209798, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      propertyPath: m_SizeDelta.y
      value: -17
      objectReference: {fileID: 0}
    - target: {fileID: 583558359841820176, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 1261233991081867013, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      propertyPath: m_AnchorMax.x
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1261233991081867013, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1261233991081867013, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      propertyPath: m_AnchorMin.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1612572572199675471, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      propertyPath: m_Pivot.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 1612572572199675471, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      propertyPath: m_Pivot.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 1612572572199675471, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      propertyPath: m_AnchorMax.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 1612572572199675471, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 1612572572199675471, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      propertyPath: m_AnchorMin.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 1612572572199675471, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 1612572572199675471, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      propertyPath: m_SizeDelta.x
      value: 800
      objectReference: {fileID: 0}
    - target: {fileID: 1612572572199675471, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      propertyPath: m_SizeDelta.y
      value: 800
      objectReference: {fileID: 0}
    - target: {fileID: 1612572572199675471, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      propertyPath: m_LocalScale.x
      value: 0.001
      objectReference: {fileID: 0}
    - target: {fileID: 1612572572199675471, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      propertyPath: m_LocalScale.y
      value: 0.001
      objectReference: {fileID: 0}
    - target: {fileID: 1612572572199675471, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      propertyPath: m_LocalScale.z
      value: 0.001
      objectReference: {fileID: 0}
    - target: {fileID: 1612572572199675471, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1612572572199675471, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1612572572199675471, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      propertyPath: m_LocalPosition.z
      value: -0.51000035
      objectReference: {fileID: 0}
    - target: {fileID: 1612572572199675471, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1612572572199675471, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1612572572199675471, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1612572572199675471, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1612572572199675471, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0.00000011920929
      objectReference: {fileID: 0}
    - target: {fileID: 1612572572199675471, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1612572572199675471, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1612572572199675471, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 180
      objectReference: {fileID: 0}
    - target: {fileID: 1612572572199675471, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1680757022505590523, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      propertyPath: m_Size
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1680757022505590523, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      propertyPath: m_Value
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1973453666559329462, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      propertyPath: m_AnchorMax.x
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1973453666559329462, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1973453666559329462, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2285869941051781478, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 2411850248514626195, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      propertyPath: m_Camera
      value: 
      objectReference: {fileID: 1152102166}
    - target: {fileID: 2411850248514626195, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      propertyPath: m_RenderMode
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 2551815063098196127, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 2768980298636603119, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      propertyPath: m_Name
      value: Inventory UI
      objectReference: {fileID: 0}
    - target: {fileID: 2768980298636603119, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 2768980298636603119, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3086526155695473934, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      propertyPath: m_Enabled
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3090547150835452407, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 3090547150835452407, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 3274292583865118922, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      propertyPath: m_AnchorMax.x
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 3274292583865118922, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      propertyPath: m_SizeDelta.x
      value: -17
      objectReference: {fileID: 0}
    - target: {fileID: 3652023849507080094, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 4553299513605244254, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      propertyPath: m_AnchorMax.x
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4553299513605244254, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4553299513605244254, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      propertyPath: m_SizeDelta.x
      value: -17
      objectReference: {fileID: 0}
    - target: {fileID: 4553299513605244254, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      propertyPath: m_SizeDelta.y
      value: -17
      objectReference: {fileID: 0}
    - target: {fileID: 4837390231406017571, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 4887612744191318534, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 5039467200435948787, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 5191891617637971402, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 5855761543411632987, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 6439942339029826799, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      propertyPath: m_Size
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6439942339029826799, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      propertyPath: m_Value
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6649600346896656400, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      propertyPath: targetInventory
      value: 
      objectReference: {fileID: 858718443}
    - target: {fileID: 6649600346896656400, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      propertyPath: inventorySlotPrefab
      value: 
      objectReference: {fileID: 5553402093153446677, guid: 760c1ab5ae139b34d8a3f2d03de344db, type: 3}
    - target: {fileID: 8504204043688463548, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0.0001967592
      objectReference: {fileID: 0}
    - target: {fileID: 8504204043688463548, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -0.0002305179
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects:
    - {fileID: 8821459119282275838, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
    m_AddedGameObjects:
    - targetCorrespondingSourceObject: {fileID: 1612572572199675471, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      insertIndex: -1
      addedObject: {fileID: 89946523}
    m_AddedComponents:
    - targetCorrespondingSourceObject: {fileID: 2768980298636603119, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      insertIndex: -1
      addedObject: {fileID: 2109280231}
    - targetCorrespondingSourceObject: {fileID: 2768980298636603119, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      insertIndex: -1
      addedObject: {fileID: 2109280228}
    - targetCorrespondingSourceObject: {fileID: 2768980298636603119, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      insertIndex: -1
      addedObject: {fileID: 2109280230}
    - targetCorrespondingSourceObject: {fileID: 5039467200435948787, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
      insertIndex: -1
      addedObject: {fileID: 1486144098}
  m_SourcePrefab: {fileID: 100100000, guid: 48984a5e7d7732b42b40e8a4521fa127, type: 3}
--- !u!1001 &3932341658291277255
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: -2856061927730038795, guid: a73860977609b004da4f63faaf514b4f, type: 3}
      propertyPath: amount
      value: 15
      objectReference: {fileID: 0}
    - target: {fileID: -2856061927730038795, guid: a73860977609b004da4f63faaf514b4f, type: 3}
      propertyPath: action.m_Name
      value: Action
      objectReference: {fileID: 0}
    - target: {fileID: -2856061927730038795, guid: a73860977609b004da4f63faaf514b4f, type: 3}
      propertyPath: examinePanelPrefab
      value: 
      objectReference: {fileID: 9014103880887437540, guid: b0444ab19c001784181726f095c3eb8c, type: 3}
    - target: {fileID: 4949094490325880866, guid: a73860977609b004da4f63faaf514b4f, type: 3}
      propertyPath: m_LocalPosition.x
      value: 3.8736727
      objectReference: {fileID: 0}
    - target: {fileID: 4949094490325880866, guid: a73860977609b004da4f63faaf514b4f, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.77
      objectReference: {fileID: 0}
    - target: {fileID: 4949094490325880866, guid: a73860977609b004da4f63faaf514b4f, type: 3}
      propertyPath: m_LocalPosition.z
      value: 8.964186
      objectReference: {fileID: 0}
    - target: {fileID: 4949094490325880866, guid: a73860977609b004da4f63faaf514b4f, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.7071067
      objectReference: {fileID: 0}
    - target: {fileID: 4949094490325880866, guid: a73860977609b004da4f63faaf514b4f, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0.7071068
      objectReference: {fileID: 0}
    - target: {fileID: 4949094490325880866, guid: a73860977609b004da4f63faaf514b4f, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4949094490325880866, guid: a73860977609b004da4f63faaf514b4f, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4949094490325880866, guid: a73860977609b004da4f63faaf514b4f, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4949094490325880866, guid: a73860977609b004da4f63faaf514b4f, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4949094490325880866, guid: a73860977609b004da4f63faaf514b4f, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5757121614970342040, guid: a73860977609b004da4f63faaf514b4f, type: 3}
      propertyPath: m_Name
      value: products_papertowels_pack
      objectReference: {fileID: 0}
    - target: {fileID: 5757121614970342040, guid: a73860977609b004da4f63faaf514b4f, type: 3}
      propertyPath: m_Layer
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: a73860977609b004da4f63faaf514b4f, type: 3}
--- !u!1001 &3949601075822213809
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 1507668053760159374, guid: b4802f192ede30045a6c782db16b2723, type: 3}
      propertyPath: action.m_Name
      value: Action
      objectReference: {fileID: 0}
    - target: {fileID: 1507668053760159374, guid: b4802f192ede30045a6c782db16b2723, type: 3}
      propertyPath: examinePanelPrefab
      value: 
      objectReference: {fileID: 9014103880887437540, guid: b0444ab19c001784181726f095c3eb8c, type: 3}
    - target: {fileID: 7455063597255174579, guid: b4802f192ede30045a6c782db16b2723, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0.21003151
      objectReference: {fileID: 0}
    - target: {fileID: 7455063597255174579, guid: b4802f192ede30045a6c782db16b2723, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7455063597255174579, guid: b4802f192ede30045a6c782db16b2723, type: 3}
      propertyPath: m_LocalPosition.z
      value: 7.3135486
      objectReference: {fileID: 0}
    - target: {fileID: 7455063597255174579, guid: b4802f192ede30045a6c782db16b2723, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.7071067
      objectReference: {fileID: 0}
    - target: {fileID: 7455063597255174579, guid: b4802f192ede30045a6c782db16b2723, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0.7071068
      objectReference: {fileID: 0}
    - target: {fileID: 7455063597255174579, guid: b4802f192ede30045a6c782db16b2723, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7455063597255174579, guid: b4802f192ede30045a6c782db16b2723, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 7455063597255174579, guid: b4802f192ede30045a6c782db16b2723, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7455063597255174579, guid: b4802f192ede30045a6c782db16b2723, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7455063597255174579, guid: b4802f192ede30045a6c782db16b2723, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7799782085419823881, guid: b4802f192ede30045a6c782db16b2723, type: 3}
      propertyPath: m_Name
      value: manhole_cover
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: b4802f192ede30045a6c782db16b2723, type: 3}
--- !u!1001 &4455869690388524289
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 1062655235285653536, guid: 4fceb36ebeeaae24e9dc6a79ee9c7607, type: 3}
      propertyPath: _userUIparent
      value: 
      objectReference: {fileID: 665718897}
    - target: {fileID: 5255025345174455308, guid: 4fceb36ebeeaae24e9dc6a79ee9c7607, type: 3}
      propertyPath: m_LocalPosition.x
      value: -5.9651747
      objectReference: {fileID: 0}
    - target: {fileID: 5255025345174455308, guid: 4fceb36ebeeaae24e9dc6a79ee9c7607, type: 3}
      propertyPath: m_LocalPosition.y
      value: 2.1910431
      objectReference: {fileID: 0}
    - target: {fileID: 5255025345174455308, guid: 4fceb36ebeeaae24e9dc6a79ee9c7607, type: 3}
      propertyPath: m_LocalPosition.z
      value: 7.484378
      objectReference: {fileID: 0}
    - target: {fileID: 5255025345174455308, guid: 4fceb36ebeeaae24e9dc6a79ee9c7607, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5255025345174455308, guid: 4fceb36ebeeaae24e9dc6a79ee9c7607, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5255025345174455308, guid: 4fceb36ebeeaae24e9dc6a79ee9c7607, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5255025345174455308, guid: 4fceb36ebeeaae24e9dc6a79ee9c7607, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5255025345174455308, guid: 4fceb36ebeeaae24e9dc6a79ee9c7607, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5255025345174455308, guid: 4fceb36ebeeaae24e9dc6a79ee9c7607, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5255025345174455308, guid: 4fceb36ebeeaae24e9dc6a79ee9c7607, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5466767361345234659, guid: 4fceb36ebeeaae24e9dc6a79ee9c7607, type: 3}
      propertyPath: m_PointAction
      value: 
      objectReference: {fileID: 4297587510407408386, guid: 5ad5980bcdd09b14a94464ddb5fb637d, type: 3}
    - target: {fileID: 5543666884352293257, guid: 4fceb36ebeeaae24e9dc6a79ee9c7607, type: 3}
      propertyPath: ActiveCamera
      value: 
      objectReference: {fileID: 26822135}
    - target: {fileID: 5543666884352293257, guid: 4fceb36ebeeaae24e9dc6a79ee9c7607, type: 3}
      propertyPath: CanvasCamera
      value: 
      objectReference: {fileID: 1152102166}
    - target: {fileID: 5543666884352293257, guid: 4fceb36ebeeaae24e9dc6a79ee9c7607, type: 3}
      propertyPath: MyEventSystem
      value: 
      objectReference: {fileID: 1390601010}
    - target: {fileID: 5543666884352293257, guid: 4fceb36ebeeaae24e9dc6a79ee9c7607, type: 3}
      propertyPath: PlayerCharacterComponent
      value: 
      objectReference: {fileID: 858718432}
    - target: {fileID: 5855394716863583731, guid: 4fceb36ebeeaae24e9dc6a79ee9c7607, type: 3}
      propertyPath: m_Name
      value: User
      objectReference: {fileID: 0}
    - target: {fileID: 9078453112623850134, guid: 4fceb36ebeeaae24e9dc6a79ee9c7607, type: 3}
      propertyPath: m_UIInputModule
      value: 
      objectReference: {fileID: 1390601011}
    - target: {fileID: 9078453112623850134, guid: 4fceb36ebeeaae24e9dc6a79ee9c7607, type: 3}
      propertyPath: m_DefaultControlScheme
      value: Keyboard&Mouse
      objectReference: {fileID: 0}
    - target: {fileID: 9078453112623850134, guid: 4fceb36ebeeaae24e9dc6a79ee9c7607, type: 3}
      propertyPath: m_NotificationBehavior
      value: 3
      objectReference: {fileID: 0}
    - target: {fileID: 9078453112623850134, guid: 4fceb36ebeeaae24e9dc6a79ee9c7607, type: 3}
      propertyPath: m_ActionEvents.Array.size
      value: 31
      objectReference: {fileID: 0}
    - target: {fileID: 9078453112623850134, guid: 4fceb36ebeeaae24e9dc6a79ee9c7607, type: 3}
      propertyPath: m_ActionEvents.Array.data[25].m_ActionId
      value: 89a524ac-c8b4-4cc2-a3fd-40d657db4d61
      objectReference: {fileID: 0}
    - target: {fileID: 9078453112623850134, guid: 4fceb36ebeeaae24e9dc6a79ee9c7607, type: 3}
      propertyPath: m_ActionEvents.Array.data[26].m_ActionId
      value: e4fae0df-911c-4038-afba-48a6bf28fa39
      objectReference: {fileID: 0}
    - target: {fileID: 9078453112623850134, guid: 4fceb36ebeeaae24e9dc6a79ee9c7607, type: 3}
      propertyPath: m_ActionEvents.Array.data[27].m_ActionId
      value: dbd13939-b365-4be7-9b44-13dbff218b30
      objectReference: {fileID: 0}
    - target: {fileID: 9078453112623850134, guid: 4fceb36ebeeaae24e9dc6a79ee9c7607, type: 3}
      propertyPath: m_ActionEvents.Array.data[28].m_ActionId
      value: 55a469c5-b3e3-4781-aac6-99a845367fb5
      objectReference: {fileID: 0}
    - target: {fileID: 9078453112623850134, guid: 4fceb36ebeeaae24e9dc6a79ee9c7607, type: 3}
      propertyPath: m_ActionEvents.Array.data[29].m_ActionId
      value: 8dab7aa4-d077-4345-accf-ccb149ad5d77
      objectReference: {fileID: 0}
    - target: {fileID: 9078453112623850134, guid: 4fceb36ebeeaae24e9dc6a79ee9c7607, type: 3}
      propertyPath: m_ActionEvents.Array.data[30].m_ActionId
      value: 2468d136-3d45-4050-966a-14b4eec74bf6
      objectReference: {fileID: 0}
    - target: {fileID: 9078453112623850134, guid: 4fceb36ebeeaae24e9dc6a79ee9c7607, type: 3}
      propertyPath: m_ActionEvents.Array.data[25].m_ActionName
      value: 'UI/Menu[/Keyboard/escape]'
      objectReference: {fileID: 0}
    - target: {fileID: 9078453112623850134, guid: 4fceb36ebeeaae24e9dc6a79ee9c7607, type: 3}
      propertyPath: m_ActionEvents.Array.data[26].m_ActionName
      value: 'UI/Inventory[/Keyboard/tab,/Keyboard/i]'
      objectReference: {fileID: 0}
    - target: {fileID: 9078453112623850134, guid: 4fceb36ebeeaae24e9dc6a79ee9c7607, type: 3}
      propertyPath: m_ActionEvents.Array.data[27].m_ActionName
      value: 'UI/LMB[/Mouse/leftButton]'
      objectReference: {fileID: 0}
    - target: {fileID: 9078453112623850134, guid: 4fceb36ebeeaae24e9dc6a79ee9c7607, type: 3}
      propertyPath: m_ActionEvents.Array.data[28].m_ActionName
      value: 'UI/MMB[/Mouse/middleButton]'
      objectReference: {fileID: 0}
    - target: {fileID: 9078453112623850134, guid: 4fceb36ebeeaae24e9dc6a79ee9c7607, type: 3}
      propertyPath: m_ActionEvents.Array.data[29].m_ActionName
      value: 'UI/RMB[/Mouse/rightButton]'
      objectReference: {fileID: 0}
    - target: {fileID: 9078453112623850134, guid: 4fceb36ebeeaae24e9dc6a79ee9c7607, type: 3}
      propertyPath: m_ActionEvents.Array.data[30].m_ActionName
      value: 'UI/Rotate[/Keyboard/r,/Keyboard/q]'
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects:
    - {fileID: 1193859726106085253, guid: 4fceb36ebeeaae24e9dc6a79ee9c7607, type: 3}
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 4fceb36ebeeaae24e9dc6a79ee9c7607, type: 3}
--- !u!1001 &6149037546204482254
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 326952316178630673, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 326952316178630673, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_AnchorMin.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 326952316178630673, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_SizeDelta.x
      value: 200
      objectReference: {fileID: 0}
    - target: {fileID: 326952316178630673, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_SizeDelta.y
      value: 50
      objectReference: {fileID: 0}
    - target: {fileID: 326952316178630673, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 100
      objectReference: {fileID: 0}
    - target: {fileID: 326952316178630673, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -125
      objectReference: {fileID: 0}
    - target: {fileID: 796807632593820649, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 796807632593820649, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 885226446643358791, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_Enabled
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1022488007040791925, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_AdditionalShaderChannelsFlag
      value: 25
      objectReference: {fileID: 0}
    - target: {fileID: 1169318483775943096, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_AnchorMax.x
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1169318483775943096, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1169318483775943096, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_AnchorMin.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1169318483775943096, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1169318483775943096, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1169318483775943096, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1169318483775943096, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_LocalScale.x
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1169318483775943096, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_LocalScale.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1169318483775943096, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_LocalScale.z
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1169318483775943096, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1169318483775943096, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1169318483775943096, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1233941134905864481, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_CellSize.x
      value: 200
      objectReference: {fileID: 0}
    - target: {fileID: 1233941134905864481, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_StartCorner
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1643247937968874612, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_AnchorMax.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 1643247937968874612, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 1643247937968874612, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_AnchorMin.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 1643247937968874612, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 1643247937968874612, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1643247937968874612, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1643247937968874612, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1768596660143021308, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1828421452105551374, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_CullingMask.m_Bits
      value: 95
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: MyInput
      value: 
      objectReference: {fileID: 1875963270}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: Run.timer
      value: 2.134077
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: Idle.timer
      value: 2.6310544
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: Jump.force
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: PlayerMono
      value: 
      objectReference: {fileID: 858718432}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: Walk.timer
      value: 0.4235747
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: MoveBinding
      value: 
      objectReference: {fileID: 11400000, guid: 4dc5d495c68f2f341a4fc5655e3a757a, type: 2}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: Air.CanOverride
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: Run.CanOverride
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: Idle.CanOverride
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: Run.acceleration
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: Walk.CanOverride
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: Ground.isGrounded
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: Idle.acceleration
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: Walk.acceleration
      value: 4
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: Ground.CanOverride
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: RunBinding.category
      value: 3
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: currentAcceleration
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: JumpBinding.category
      value: 3
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: MoveBinding.MaxCalls
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: MoveBinding.category
      value: 3
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: DirectionCastOrigin.y
      value: -1.1
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: MoveBinding.TimesCall
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: MoveBinding.cooldownTime
      value: 10
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: MoveBinding.cooldownType
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: Run.staminaSpecs.recover
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: Idle.staminaSpecs.recover
      value: 10
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: RunBinding.ActionReference
      value: 
      objectReference: {fileID: -2305340066470693299, guid: 5ad5980bcdd09b14a94464ddb5fb637d, type: 3}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: JumpBinding.ActionReference
      value: 
      objectReference: {fileID: -7041372686476093287, guid: 5ad5980bcdd09b14a94464ddb5fb637d, type: 3}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: MoveBinding.ActionReference
      value: 
      objectReference: {fileID: 47184797105263802, guid: 5ad5980bcdd09b14a94464ddb5fb637d, type: 3}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: MoveBinding.CooldownDurationSeconds
      value: 10
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: RunBinding.RegisterOnPressedCallback
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: JumpBinding.RegisterOnPressedCallback
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: MoveBinding.RegisterOnPressedCallback
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: RunBinding.RegisterOnReleasedCallback
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: JumpBinding.RegisterOnReleasedCallback
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: MoveBinding.RegisterOnReleasedCallback
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: RunBinding.RegisterOnPerformedCallback
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: JumpBinding.RegisterOnPerformedCallback
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: MoveBinding.RegisterOnPerformedCallback
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: RunBinding.RegisterValueChangedCallback
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: MoveBinding.RegisterValueChangedCallback
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: Run.accelerationCurve.m_Curve.Array.size
      value: 4
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: Idle.accelerationCurve.m_Curve.Array.size
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: Walk.accelerationCurve.m_Curve.Array.size
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: currentAccelerationCurve.m_Curve.Array.size
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: Run.accelerationCurve.m_Curve.Array.data[1].time
      value: 0.25096318
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: Run.accelerationCurve.m_Curve.Array.data[2].time
      value: 0.6716688
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: Run.accelerationCurve.m_Curve.Array.data[3].time
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: Idle.accelerationCurve.m_Curve.Array.data[1].time
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: Run.accelerationCurve.m_Curve.Array.data[1].value
      value: 0.66799045
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: Run.accelerationCurve.m_Curve.Array.data[2].value
      value: 0.90676886
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: Run.accelerationCurve.m_Curve.Array.data[3].value
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: Walk.accelerationCurve.m_Curve.Array.data[1].time
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: Idle.accelerationCurve.m_Curve.Array.data[1].value
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: Walk.accelerationCurve.m_Curve.Array.data[0].value
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: Walk.accelerationCurve.m_Curve.Array.data[1].value
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: Run.accelerationCurve.m_Curve.Array.data[1].inSlope
      value: 0.9019263
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: Run.accelerationCurve.m_Curve.Array.data[2].inSlope
      value: 0.8282441
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: Run.accelerationCurve.m_Curve.Array.data[3].inSlope
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: currentAccelerationCurve.m_Curve.Array.data[1].time
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: Idle.accelerationCurve.m_Curve.Array.data[0].inSlope
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: Idle.accelerationCurve.m_Curve.Array.data[1].inSlope
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: Run.accelerationCurve.m_Curve.Array.data[1].inWeight
      value: 0.33333334
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: Run.accelerationCurve.m_Curve.Array.data[1].outSlope
      value: 0.9019263
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: Run.accelerationCurve.m_Curve.Array.data[2].inWeight
      value: 0.33333334
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: Run.accelerationCurve.m_Curve.Array.data[2].outSlope
      value: 0.8282441
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: Run.accelerationCurve.m_Curve.Array.data[3].outSlope
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: Walk.accelerationCurve.m_Curve.Array.data[0].inSlope
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: Walk.accelerationCurve.m_Curve.Array.data[1].inSlope
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: currentAccelerationCurve.m_Curve.Array.data[0].value
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: currentAccelerationCurve.m_Curve.Array.data[1].value
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: Idle.accelerationCurve.m_Curve.Array.data[0].outSlope
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: Idle.accelerationCurve.m_Curve.Array.data[1].outSlope
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: Run.accelerationCurve.m_Curve.Array.data[1].outWeight
      value: 0.33333334
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: Run.accelerationCurve.m_Curve.Array.data[2].outWeight
      value: 0.4907367
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: Walk.accelerationCurve.m_Curve.Array.data[0].outSlope
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: Walk.accelerationCurve.m_Curve.Array.data[1].outSlope
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: currentAccelerationCurve.m_Curve.Array.data[0].inSlope
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: currentAccelerationCurve.m_Curve.Array.data[1].inSlope
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: currentAccelerationCurve.m_Curve.Array.data[0].outSlope
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: currentAccelerationCurve.m_Curve.Array.data[1].outSlope
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: Idle.accelerationCurve.m_Curve.Array.data[0].tangentMode
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: Idle.accelerationCurve.m_Curve.Array.data[1].tangentMode
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: Walk.accelerationCurve.m_Curve.Array.data[0].tangentMode
      value: 34
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: Walk.accelerationCurve.m_Curve.Array.data[1].tangentMode
      value: 34
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: currentAccelerationCurve.m_Curve.Array.data[0].tangentMode
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2250847455398730690, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: currentAccelerationCurve.m_Curve.Array.data[1].tangentMode
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2786292861261399877, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_Name
      value: Player
      objectReference: {fileID: 0}
    - target: {fileID: 2786292861261399877, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 3474384654803765789, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_LocalPosition.x
      value: 9.91
      objectReference: {fileID: 0}
    - target: {fileID: 3474384654803765789, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_LocalPosition.y
      value: 2.07
      objectReference: {fileID: 0}
    - target: {fileID: 3474384654803765789, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_LocalPosition.z
      value: 4.53
      objectReference: {fileID: 0}
    - target: {fileID: 3474384654803765789, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 3474384654803765789, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 3474384654803765789, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 3474384654803765789, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 3474384654803765789, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3474384654803765789, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3474384654803765789, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4102958411330190334, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: MyInput
      value: 
      objectReference: {fileID: 1875963270}
    - target: {fileID: 4102958411330190334, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_Enabled
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4102958411330190334, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: senbility
      value: 0.2
      objectReference: {fileID: 0}
    - target: {fileID: 4102958411330190334, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: cameraHolder
      value: 
      objectReference: {fileID: 1548139918}
    - target: {fileID: 4102958411330190334, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: playerCamera
      value: 
      objectReference: {fileID: 26822135}
    - target: {fileID: 4102958411330190334, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: dragHoldPoint
      value: 
      objectReference: {fileID: 550914658}
    - target: {fileID: 4102958411330190334, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: headBobManager
      value: 
      objectReference: {fileID: 287183106}
    - target: {fileID: 4102958411330190334, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: RunBob.infinity
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4102958411330190334, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: IdleBob.infinity
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4102958411330190334, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: WalkBob.infinity
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4102958411330190334, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: LookBinding.category
      value: 3
      objectReference: {fileID: 0}
    - target: {fileID: 4102958411330190334, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: InteractionPointTransform
      value: 
      objectReference: {fileID: 550914658}
    - target: {fileID: 4102958411330190334, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: LookBinding.ActionReference
      value: 
      objectReference: {fileID: 8788670137154484018, guid: 5ad5980bcdd09b14a94464ddb5fb637d, type: 3}
    - target: {fileID: 4102958411330190334, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: LookBinding.RegisterOnPressedCallback
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4102958411330190334, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: LookBinding.RegisterOnReleasedCallback
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4102958411330190334, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: LookBinding.RegisterOnPerformedCallback
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4102958411330190334, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: LookBinding.RegisterValueChangedCallback
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4213880992251765459, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4213880992251765459, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4213880992251765459, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4213880992251765459, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4213880992251765459, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4413731248046837983, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4413731248046837983, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_AnchorMin.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4413731248046837983, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_SizeDelta.x
      value: 200
      objectReference: {fileID: 0}
    - target: {fileID: 4413731248046837983, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_SizeDelta.y
      value: 50
      objectReference: {fileID: 0}
    - target: {fileID: 4413731248046837983, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 100
      objectReference: {fileID: 0}
    - target: {fileID: 4413731248046837983, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -25
      objectReference: {fileID: 0}
    - target: {fileID: 4705984108871589067, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: User
      value: 
      objectReference: {fileID: 1875963268}
    - target: {fileID: 4705984108871589067, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: MyInput
      value: 
      objectReference: {fileID: 1875963270}
    - target: {fileID: 4705984108871589067, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: UIHolder
      value: 
      objectReference: {fileID: 427868379}
    - target: {fileID: 4705984108871589067, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: inventory
      value: 
      objectReference: {fileID: 858718443}
    - target: {fileID: 4705984108871589067, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: PlayerCameraHolder
      value: 
      objectReference: {fileID: 1548139918}
    - target: {fileID: 4705984108871589067, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: MenuBinding.Override
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4705984108871589067, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: MenuBinding.category
      value: 3
      objectReference: {fileID: 0}
    - target: {fileID: 4705984108871589067, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: MenuBinding.ActionReference
      value: 
      objectReference: {fileID: -951594803642964015, guid: 5ad5980bcdd09b14a94464ddb5fb637d, type: 3}
    - target: {fileID: 4705984108871589067, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: MenuBinding.RegisterOnPressedCallback
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4705984108871589067, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: MenuBinding.RegisterOnReleasedCallback
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4705984108871589067, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: MenuBinding.RegisterOnPerformedCallback
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4705984108871589067, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: MenuBinding.RegisterValueChangedCallback
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: loleffect
      value: 5625796945308025164
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: tiredEffect
      value: -2
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: effectManager
      value: 
      objectReference: {fileID: 858718439}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: playerMovement
      value: 
      objectReference: {fileID: 858718435}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: breathlessEffect
      value: -2
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: breathlessEffect.icon
      value: 
      objectReference: {fileID: 21300000, guid: 29ac10d7b28795744a4b27db448454d0, type: 3}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: effectManager.playerMono
      value: 
      objectReference: {fileID: 858718432}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: breathlessEffect.effectName
      value: Breathless
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: breathlessEffect.description
      value: Take a deep breath
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: 'managedReferences[5625796945308025151]'
      value: Assembly-CSharp BreathlessEffect
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: 'managedReferences[5625796945308025152]'
      value: Assembly-CSharp DizzinessEffect
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: 'managedReferences[5625796945308025153]'
      value: Assembly-CSharp HighHeartRateEffect
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: 'managedReferences[5625796945308025154]'
      value: Assembly-CSharp LowSanityTripEffect
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: 'managedReferences[5625796945308025155]'
      value: Assembly-CSharp StatusEffect
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: 'managedReferences[5625796945308025156]'
      value: Assembly-CSharp HighHeartRateEffect
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: 'managedReferences[5625796945308025157]'
      value: Assembly-CSharp DizzinessEffect
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: 'managedReferences[5625796945308025158]'
      value: Assembly-CSharp BreathlessEffect
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: 'managedReferences[5625796945308025159]'
      value: Assembly-CSharp HighHeartRateEffect
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: 'managedReferences[5625796945308025161]'
      value: Assembly-CSharp BreathlessEffect
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: 'managedReferences[5625796945308025163]'
      value: Assembly-CSharp DizzinessEffect
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: 'managedReferences[5625796945308025164]'
      value: Assembly-CSharp HighHeartRateEffect
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: breathlessEffect.headBobData.rotationMax.x
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: breathlessEffect.headBobData.rotationSpeed
      value: 2.6
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025151].icon
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025152].icon
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025153].icon
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025154].icon
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025155].icon
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025156].icon
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025157].icon
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025158].icon
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025159].icon
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025161].icon
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025163].icon
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025164].icon
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025151].isActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025152].duration
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025152].isActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025153].isActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025154].isActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025155].isActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025156].isActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025157].duration
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025157].isActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025158].isActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025159].isActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025161].isActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025163].duration
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025163].isActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025164].isActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025152].amplitude
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025152].frequency
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025157].amplitude
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025157].frequency
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025163].amplitude
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025163].frequency
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025151].effectName
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025151].shakeForce
      value: 0.3
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025152].effectName
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025153].effectName
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025154].effectName
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025155].effectName
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025156].effectName
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025157].effectName
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025158].effectName
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025158].shakeForce
      value: 0.3
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025159].effectName
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025161].effectName
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025161].shakeForce
      value: 0.3
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025163].effectName
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025164].effectName
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025151].description
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025152].description
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025153].description
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025154].description
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025155].description
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025156].description
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025157].description
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025158].description
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025159].description
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025161].description
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025163].description
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025164].description
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025153].AffectVision
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025153].rateDecrease
      value: 0.01
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025156].AffectVision
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025156].rateDecrease
      value: 0.01
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025159].AffectVision
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025159].rateDecrease
      value: 0.01
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025164].AffectVision
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025164].rateDecrease
      value: 0.01
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025153].AffectStamina
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025156].AffectStamina
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025159].AffectStamina
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025164].AffectStamina
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025153].AffectBreathing
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025156].AffectBreathing
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025159].AffectBreathing
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025164].AffectBreathing
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025151].oscillationSpeed
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025158].oscillationSpeed
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025161].oscillationSpeed
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025153].headBobData.speed
      value: 0.01
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025156].headBobData.speed
      value: 0.01
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025159].headBobData.speed
      value: 0.01
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025164].headBobData.speed
      value: 0.01
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025153].headBobData.amount
      value: 0.01
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025156].headBobData.amount
      value: 0.01
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025159].headBobData.amount
      value: 0.01
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025164].headBobData.amount
      value: 0.01
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025153].headBobData.duration
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025153].headBobData.infinity
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025156].headBobData.duration
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025156].headBobData.infinity
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025159].headBobData.duration
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025159].headBobData.infinity
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025164].headBobData.duration
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025164].headBobData.infinity
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025153].breathlessEffect.icon
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025153].conditionThresholds.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025153].conditionThresholds.y
      value: 25
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025154].conditionThresholds.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025154].conditionThresholds.y
      value: 25
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025156].breathlessEffect.icon
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025156].conditionThresholds.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025156].conditionThresholds.y
      value: 25
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025159].breathlessEffect.icon
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025159].conditionThresholds.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025159].conditionThresholds.y
      value: 25
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025164].breathlessEffect.icon
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025164].conditionThresholds.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025164].conditionThresholds.y
      value: 25
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025153].breathlessEffect.isActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025156].breathlessEffect.isActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025159].breathlessEffect.isActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025164].breathlessEffect.isActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025153].breathlessEffect.effectName
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025153].breathlessEffect.shakeForce
      value: 0.3
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025156].breathlessEffect.effectName
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025156].breathlessEffect.shakeForce
      value: 0.3
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025159].breathlessEffect.effectName
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025159].breathlessEffect.shakeForce
      value: 0.3
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025164].breathlessEffect.effectName
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025164].breathlessEffect.shakeForce
      value: 0.3
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025153].breathlessEffect.description
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025156].breathlessEffect.description
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025159].breathlessEffect.description
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025164].breathlessEffect.description
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025153].breathlessEffect.oscillationSpeed
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025156].breathlessEffect.oscillationSpeed
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025159].breathlessEffect.oscillationSpeed
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 5553851229431007914, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: managedReferences[5625796945308025164].breathlessEffect.oscillationSpeed
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 5698927493845960410, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_Enabled
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5820757280525948126, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5984072456629612481, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5984072456629612481, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_AnchorMin.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5984072456629612481, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_SizeDelta.x
      value: 200
      objectReference: {fileID: 0}
    - target: {fileID: 5984072456629612481, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_SizeDelta.y
      value: 50
      objectReference: {fileID: 0}
    - target: {fileID: 5984072456629612481, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 100
      objectReference: {fileID: 0}
    - target: {fileID: 5984072456629612481, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -75
      objectReference: {fileID: 0}
    - target: {fileID: 6104574021564369951, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: orthographic
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6104574021564369951, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_OcclusionCulling
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6104574021564369951, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_CullingMask.m_Bits
      value: 32
      objectReference: {fileID: 0}
    - target: {fileID: 6160702627738662208, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: progress
      value: 
      objectReference: {fileID: 365720266}
    - target: {fileID: 6953009525389079465, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_AnchorMax.x
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6953009525389079465, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6953009525389079465, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_AnchorMin.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6953009525389079465, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6953009525389079465, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6953009525389079465, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6953009525389079465, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6953009525389079465, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7140868019181205512, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_RenderMode
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7140868019181205512, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_VertexColorAlwaysGammaSpace
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7488457649872112934, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_Pivot.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7488457649872112934, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_Pivot.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7488457649872112934, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7488457649872112934, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7488457649872112934, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_LocalScale.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7488457649872112934, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_LocalScale.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7488457649872112934, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_LocalScale.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7488457649872112934, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7488457649872112934, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7488457649872112934, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7488457649872112934, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7488457649872112934, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7488457649872112934, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7831802660052190827, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 8229108557128630271, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 8770142762446890730, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 8770142762446890730, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_AnchorMin.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 8770142762446890730, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_SizeDelta.x
      value: 200
      objectReference: {fileID: 0}
    - target: {fileID: 8770142762446890730, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_SizeDelta.y
      value: 50
      objectReference: {fileID: 0}
    - target: {fileID: 8770142762446890730, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 100
      objectReference: {fileID: 0}
    - target: {fileID: 8770142762446890730, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -175
      objectReference: {fileID: 0}
    - target: {fileID: 9168499450705671705, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 9168499450705671705, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.000000059604645
      objectReference: {fileID: 0}
    - target: {fileID: 9168499450705671705, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents:
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    m_RemovedGameObjects:
    - {fileID: 1768596660143021308, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
    m_AddedGameObjects:
    - targetCorrespondingSourceObject: {fileID: 3474384654803765789, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      insertIndex: -1
      addedObject: {fileID: 1625563412}
    - targetCorrespondingSourceObject: {fileID: 578454909220119397, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      insertIndex: -1
      addedObject: {fileID: 550914658}
    - targetCorrespondingSourceObject: {fileID: 476192706533227383, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      insertIndex: -1
      addedObject: {fileID: 325580535}
    - targetCorrespondingSourceObject: {fileID: 1169318483775943096, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      insertIndex: -1
      addedObject: {fileID: 365720264}
    m_AddedComponents:
    - targetCorrespondingSourceObject: {fileID: 2786292861261399877, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      insertIndex: -1
      addedObject: {fileID: 858718439}
    - targetCorrespondingSourceObject: {fileID: 2786292861261399877, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      insertIndex: -1
      addedObject: {fileID: 858718443}
    - targetCorrespondingSourceObject: {fileID: 6631933611597660996, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      insertIndex: -1
      addedObject: {fileID: 287183105}
    - targetCorrespondingSourceObject: {fileID: 6631933611597660996, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      insertIndex: -1
      addedObject: {fileID: 287183106}
    - targetCorrespondingSourceObject: {fileID: 5820757280525948126, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      insertIndex: -1
      addedObject: {fileID: 26822134}
    - targetCorrespondingSourceObject: {fileID: 7831802660052190827, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      insertIndex: -1
      addedObject: {fileID: 665718896}
    - targetCorrespondingSourceObject: {fileID: 8103734383454721524, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      insertIndex: -1
      addedObject: {fileID: 910774345}
    - targetCorrespondingSourceObject: {fileID: 8229108557128630271, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
      insertIndex: -1
      addedObject: {fileID: 1339507345}
  m_SourcePrefab: {fileID: 100100000, guid: bbb74d2d4f54dbd4ca43c96f84f7c1a6, type: 3}
--- !u!1660057539 &9223372036854775807
SceneRoots:
  m_ObjectHideFlags: 0
  m_Roots:
  - {fileID: 410087041}
  - {fileID: 832575519}
  - {fileID: 463230266}
  - {fileID: 6149037546204482254}
  - {fileID: 4455869690388524289}
  - {fileID: 1233695913}
  - {fileID: 1216155667}
  - {fileID: 1756558043}
  - {fileID: 680254468}
  - {fileID: 1780177147944756748}
  - {fileID: 3949601075822213809}
  - {fileID: 2476892175714858251}
  - {fileID: 3932341658291277255}
  - {fileID: 1666664377295561245}
  - {fileID: 2115109471}
  - {fileID: 6576953}
  - {fileID: 1962102720}
