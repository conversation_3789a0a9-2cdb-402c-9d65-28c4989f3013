using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.InputSystem.UI;
using UnityEditor;
using UnityEditor.Animations;
using System.IO;
using Newtonsoft.Json;

public partial class PlayerMonoBehaviour : MonoBehaviour, IPlayer
{   
  public void ToggleUIPanel(GameObject panel) => User.UIManager.ToggleUIPanel(panel);

  public PlayerStatus GetStatus() => status;
  
  public PlayerMovement Movement() => movement;
  public FPSController FirstPersonCamera() => firstPersonCamera;
  
  public DynamicIKManager GetDynamicIKManager() => playerIk;
  
  public Rigidbody Rigidbody() => PlayerRigidbody;
  public Transform CameraHolder() => PlayerCameraHolder;
  public Transform GetUIHolder() => UIHolder;
  public Animator GetAnimator() => animator;
  public AnimatorController GetControllerAtual() => ControllerAtual;
  public AnimatorController GetControllerBase() => ControllerBase;

  public CrosshairManager CrossHair => firstPersonCamera.crosshairManager;

  public UserMonoBehaviour GetUser(){ return User;}
  public PlayerInputHandler GetInputHandler(){ return MyInput;}
  public PlayerMonoBehaviour GetPlayerComponent(){ return this;}
  public IInventory Inventory(){ Debug.Log("PlayerMonoBehaviour: Inventory()"); return inventory;}
  public GameObject GetGameObject(){ return gameObject;}
  public Camera GetCamera(){ return PlayerCamera;}
  public Camera GetCanvasCamera(){ return CanvasCamera;}
  public Canvas GetUICanvas(){ return null;}
}