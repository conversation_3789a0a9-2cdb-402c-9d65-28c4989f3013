using UnityEngine;
using UnityEngine.InputSystem;
using UnityEditor;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;

[System.Serializable]
public class InteractHintData
{
    public string InteractionText;
    public Sprite InteractionSprite;

    [Tooltip("If true, the interaction text will be displayed with animation")]
    public bool UseAnimatedText;

    [<PERSON><PERSON>("Animated Text Configuration")]
    [Toolt<PERSON>("Pre-configured VisualTextDocument for complex animated text. If set, this will be used instead of parsing InteractionText.")]
    public VisualTextDocument AnimatedTextDocument;

    [Tooltip("Text effects to apply when using animated text (only used if AnimatedTextDocument is null)")]
    public List<TextEffectBase> TextEffects = new List<TextEffectBase>();

    [Header("Typewriter Settings")]
    [Tooltip("Use typewriter effect for animated text")]
    public bool UseTypewriter = true;

    [Tooltip("Delay between each character when using typewriter effect")]
    public float DelayPerCharacter = 0.05f;

    [<PERSON><PERSON><PERSON>("Color for the animated text")]
    public Color TextColor = Color.white;

    [<PERSON>lt<PERSON>("Font size for the animated text")]
    public float FontSize = 18f;
}


