using UnityEngine;
using UnityEngine.InputSystem;
using UnityEditor;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;

[System.Serializable]
public class InteractHintData
{
    public string InteractionText;
    public Sprite InteractionSprite;

    [Tooltip("If true, the interaction text will be displayed with animation")]
    public bool UseAnimatedText;
}


