using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Collections.Generic;

public class CrosshairManager : MonoBehaviour
{
    [<PERSON><PERSON>("Crosshair Visual")]
    public Image CrosshairImage;
    public Sprite DefaultSprite;
    public Sprite InteractableSprite;
    
    [<PERSON>er("Crosshair Text")]
    public TextMeshProUGUI CrosshairText;
    public GameObject CrosshairTextBackground;
    
    [Header("Animation Settings")]
    public float transitionSpeed = 5f;
    
    private bool isShowingInteractable = false;

    [<PERSON><PERSON>("Animated Text System")]
    [Tooltip("VisualTextDisplayer component for animated crosshair text")]
    public VisualTextDisplayer animatedTextDisplayer;

    [Tooltip("Default text effects to apply when no specific effects are configured")]
    public List<TextEffectBase> defaultTextEffects = new List<TextEffectBase>();

    // Cache for created documents to avoid memory leaks
    private VisualTextDocument _currentAnimatedDocument;

    void Start()
    {
        SetDefaultCrosshair();
    }

    void OnDestroy()
    {
        // Clean up any created documents to prevent memory leaks
        CleanupAnimatedDocument();
    }
    
    public void SetDefaultCrosshair()
    {
        if (CrosshairImage != null && DefaultSprite != null)
        {
            CrosshairImage.sprite = DefaultSprite;
        }
        
        if (CrosshairText != null)
        {
            CrosshairText.text = "";
            CrosshairText.gameObject.SetActive(false);
        }
        
        if (CrosshairTextBackground != null)
        {
            CrosshairTextBackground.SetActive(false);
        }

        if (animatedTextDisplayer != null)
        {
            animatedTextDisplayer.StopDisplay();
        }

        // Clean up any created animated document
        CleanupAnimatedDocument();

        isShowingInteractable = false;
    }
    
    public void SetInteractableCrosshair(InteractHintData hint)
    {
        Debug.Log("SetInteractableCrosshair");

        if (hint == null)
        {
            SetDefaultCrosshair();
            return;
        }

        // Atualiza sprite do crosshair
        if (CrosshairImage != null)
        {
            Sprite spriteToUse = hint.InteractionSprite != null ? hint.InteractionSprite : InteractableSprite;
            if (spriteToUse != null)
            {
                CrosshairImage.sprite = spriteToUse;
            }
        }

        // Decide entre usar texto animado ou estático
        if (hint.UseAnimatedText && animatedTextDisplayer != null)
        {
            CrosshairText.gameObject.SetActive(false);
            if (CrosshairTextBackground != null)
                CrosshairTextBackground.SetActive(false);

            DisplayAnimatedText(hint);
        }
        else
        {
            // Texto puro
            if (!string.IsNullOrEmpty(hint.InteractionText))
            {
                CrosshairText.text = hint.InteractionText;
                CrosshairText.gameObject.SetActive(true);
                if (CrosshairTextBackground != null)
                    CrosshairTextBackground.SetActive(true);
            }
            else
            {
                CrosshairText.gameObject.SetActive(false);
                if (CrosshairTextBackground != null)
                    CrosshairTextBackground.SetActive(false);
            }

            if (animatedTextDisplayer != null)
            {
                animatedTextDisplayer.StopDisplay();
            }

            // Clean up any previous animated document
            CleanupAnimatedDocument();
        }

        isShowingInteractable = true;
    }

    /// <summary>
    /// Displays animated text using the enhanced text effect system
    /// </summary>
    private void DisplayAnimatedText(InteractHintData hint)
    {
        // Clean up any previous document first
        CleanupAnimatedDocument();

        // Use pre-configured document if available
        if (hint.AnimatedTextDocument != null)
        {
            animatedTextDisplayer.Display(hint.AnimatedTextDocument);
            return;
        }

        // Create a new document with the hint's configuration
        _currentAnimatedDocument = CreateAnimatedDocument(hint);
        if (_currentAnimatedDocument != null)
        {
            animatedTextDisplayer.Display(_currentAnimatedDocument);
        }
    }

    /// <summary>
    /// Creates a VisualTextDocument from InteractHintData configuration
    /// </summary>
    private VisualTextDocument CreateAnimatedDocument(InteractHintData hint)
    {
        if (string.IsNullOrEmpty(hint.InteractionText))
            return null;

        var document = ScriptableObject.CreateInstance<VisualTextDocument>();

        // Configure document defaults
        document.DefaultTextColor = hint.TextColor;
        document.DefaultFontSize = hint.FontSize;
        document.DefaultUseTypewriter = hint.UseTypewriter;
        document.DefaultDelayPerCharacter = hint.DelayPerCharacter;

        // Parse the text
        document.ParseAndConfigureText(hint.InteractionText);

        // Apply text effects if specified
        ApplyTextEffectsToDocument(document, hint);

        return document;
    }

    /// <summary>
    /// Applies text effects from hint configuration to the document
    /// </summary>
    private void ApplyTextEffectsToDocument(VisualTextDocument document, InteractHintData hint)
    {
        // Determine which effects to use
        List<TextEffectBase> effectsToApply = new List<TextEffectBase>();

        if (hint.TextEffects != null && hint.TextEffects.Count > 0)
        {
            effectsToApply.AddRange(hint.TextEffects);
        }
        else if (defaultTextEffects != null && defaultTextEffects.Count > 0)
        {
            effectsToApply.AddRange(defaultTextEffects);
        }

        // Apply effects to all sentences in the document
        if (effectsToApply.Count > 0)
        {
            foreach (var phrase in document.Phrases)
            {
                foreach (var sentence in phrase.Sentences)
                {
                    if (sentence.TextEffects == null)
                        sentence.TextEffects = new List<TextEffectBase>();

                    sentence.TextEffects.AddRange(effectsToApply);
                }
            }
        }
    }

    /// <summary>
    /// Cleans up the current animated document to prevent memory leaks
    /// </summary>
    private void CleanupAnimatedDocument()
    {
        if (_currentAnimatedDocument != null)
        {
            if (Application.isPlaying)
            {
                DestroyImmediate(_currentAnimatedDocument);
            }
            else
            {
                DestroyImmediate(_currentAnimatedDocument);
            }
            _currentAnimatedDocument = null;
        }
    }

    public bool IsShowingInteractable()
    {
        return isShowingInteractable;
    }
}
