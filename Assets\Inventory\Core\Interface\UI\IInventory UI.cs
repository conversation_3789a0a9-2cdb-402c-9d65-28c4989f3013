using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using System.Linq;

public interface IInventoryUI
{
    public event System.Action OnUpdateUI;
    public event System.Action OnOpenInventory;
    public event System.Action OnCloseInventory;

    public void UpdateUI();
    public List<IInventorySlotUI> GetSlotUIs();
    public IInventorySlotUI GetSlotUI(int index = -1);
    public IInventory GetInventory();
    public RectTransform GetViewPort();
}